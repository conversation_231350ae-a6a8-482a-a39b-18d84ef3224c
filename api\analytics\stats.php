<?php
require_once '../config/database.php';
require_once '../config/auth.php';

/**
 * API des statistiques et analyses
 */

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
    exit();
}

try {
    $auth = new Auth();
    $permissions = new Permissions();
    $db = new DatabaseHelper();
    
    // Vérification de l'authentification
    $user = $auth->requireAuth();
    
    // Paramètres de requête
    $period = $_GET['period'] ?? '30d'; // 7d, 30d, 90d, 1y
    $platform = $_GET['platform'] ?? 'all';
    $teamId = $_GET['team_id'] ?? null;
    $metric = $_GET['metric'] ?? 'overview'; // overview, engagement, reach, growth
    
    // Validation des paramètres
    $validPeriods = ['7d', '30d', '90d', '1y'];
    if (!in_array($period, $validPeriods)) {
        $period = '30d';
    }
    
    $validMetrics = ['overview', 'engagement', 'reach', 'growth', 'posts', 'platforms'];
    if (!in_array($metric, $validMetrics)) {
        $metric = 'overview';
    }
    
    // Vérification des permissions pour l'équipe
    if ($teamId && !$permissions->canAccessTeam($user['id'], $teamId)) {
        throw new Exception('Vous n\'avez pas accès à cette équipe');
    }
    
    // Calcul des dates
    $dates = calculateDateRange($period);
    
    // Récupération des données selon le type de métrique
    $data = [];
    
    switch ($metric) {
        case 'overview':
            $data = getOverviewStats($db, $user['id'], $teamId, $platform, $dates);
            break;
            
        case 'engagement':
            $data = getEngagementStats($db, $user['id'], $teamId, $platform, $dates);
            break;
            
        case 'reach':
            $data = getReachStats($db, $user['id'], $teamId, $platform, $dates);
            break;
            
        case 'growth':
            $data = getGrowthStats($db, $user['id'], $teamId, $platform, $dates);
            break;
            
        case 'posts':
            $data = getPostsStats($db, $user['id'], $teamId, $platform, $dates);
            break;
            
        case 'platforms':
            $data = getPlatformsStats($db, $user['id'], $teamId, $dates);
            break;
    }
    
    // Réponse de succès
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'data' => $data,
        'filters' => [
            'period' => $period,
            'platform' => $platform,
            'team_id' => $teamId,
            'metric' => $metric,
            'date_range' => $dates
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Calcule la plage de dates selon la période
 */
function calculateDateRange($period) {
    $endDate = date('Y-m-d');
    
    switch ($period) {
        case '7d':
            $startDate = date('Y-m-d', strtotime('-7 days'));
            break;
        case '30d':
            $startDate = date('Y-m-d', strtotime('-30 days'));
            break;
        case '90d':
            $startDate = date('Y-m-d', strtotime('-90 days'));
            break;
        case '1y':
            $startDate = date('Y-m-d', strtotime('-1 year'));
            break;
        default:
            $startDate = date('Y-m-d', strtotime('-30 days'));
    }
    
    return [
        'start_date' => $startDate,
        'end_date' => $endDate
    ];
}

/**
 * Construit la clause WHERE pour filtrer par utilisateur/équipe/plateforme
 */
function buildWhereClause($userId, $teamId, $platform) {
    $whereConditions = ['p.user_id = ?'];
    $params = [$userId];
    
    if ($teamId) {
        $whereConditions[] = 'p.team_id = ?';
        $params[] = $teamId;
    }
    
    if ($platform !== 'all') {
        $whereConditions[] = 'sa.platform = ?';
        $params[] = $platform;
    }
    
    return [
        'where' => implode(' AND ', $whereConditions),
        'params' => $params
    ];
}

/**
 * Statistiques générales
 */
function getOverviewStats($db, $userId, $teamId, $platform, $dates) {
    $filter = buildWhereClause($userId, $teamId, $platform);
    
    // Publications totales
    $totalPosts = $db->fetchOne(
        "SELECT COUNT(*) as count FROM posts p
         JOIN scheduled_posts sp ON p.id = sp.post_id
         JOIN social_accounts sa ON sp.social_account_id = sa.id
         WHERE {$filter['where']} AND p.published_at BETWEEN ? AND ?",
        array_merge($filter['params'], [$dates['start_date'], $dates['end_date']])
    );
    
    // Engagement total
    $totalEngagement = $db->fetchOne(
        "SELECT 
            SUM(a.likes_count) as total_likes,
            SUM(a.comments_count) as total_comments,
            SUM(a.shares_count) as total_shares,
            SUM(a.views_count) as total_views,
            AVG(a.engagement_rate) as avg_engagement
         FROM analytics a
         JOIN posts p ON a.post_id = p.id
         JOIN social_accounts sa ON a.social_account_id = sa.id
         WHERE {$filter['where']} AND a.recorded_at BETWEEN ? AND ?",
        array_merge($filter['params'], [$dates['start_date'], $dates['end_date']])
    );
    
    // Portée totale
    $totalReach = $db->fetchOne(
        "SELECT SUM(a.reach_count) as total_reach, SUM(a.impressions_count) as total_impressions
         FROM analytics a
         JOIN posts p ON a.post_id = p.id
         JOIN social_accounts sa ON a.social_account_id = sa.id
         WHERE {$filter['where']} AND a.recorded_at BETWEEN ? AND ?",
        array_merge($filter['params'], [$dates['start_date'], $dates['end_date']])
    );
    
    // Évolution quotidienne
    $dailyStats = $db->fetchAll(
        "SELECT 
            DATE(a.recorded_at) as date,
            COUNT(DISTINCT p.id) as posts_count,
            SUM(a.likes_count + a.comments_count + a.shares_count) as total_engagement,
            SUM(a.reach_count) as total_reach
         FROM analytics a
         JOIN posts p ON a.post_id = p.id
         JOIN social_accounts sa ON a.social_account_id = sa.id
         WHERE {$filter['where']} AND a.recorded_at BETWEEN ? AND ?
         GROUP BY DATE(a.recorded_at)
         ORDER BY date",
        array_merge($filter['params'], [$dates['start_date'], $dates['end_date']])
    );
    
    return [
        'summary' => [
            'total_posts' => (int)$totalPosts['count'],
            'total_likes' => (int)($totalEngagement['total_likes'] ?? 0),
            'total_comments' => (int)($totalEngagement['total_comments'] ?? 0),
            'total_shares' => (int)($totalEngagement['total_shares'] ?? 0),
            'total_views' => (int)($totalEngagement['total_views'] ?? 0),
            'avg_engagement_rate' => round($totalEngagement['avg_engagement'] ?? 0, 2),
            'total_reach' => (int)($totalReach['total_reach'] ?? 0),
            'total_impressions' => (int)($totalReach['total_impressions'] ?? 0)
        ],
        'daily_stats' => $dailyStats
    ];
}

/**
 * Statistiques d'engagement
 */
function getEngagementStats($db, $userId, $teamId, $platform, $dates) {
    $filter = buildWhereClause($userId, $teamId, $platform);
    
    // Engagement par type
    $engagementByType = $db->fetchAll(
        "SELECT 
            'likes' as type, SUM(a.likes_count) as count
         FROM analytics a
         JOIN posts p ON a.post_id = p.id
         JOIN social_accounts sa ON a.social_account_id = sa.id
         WHERE {$filter['where']} AND a.recorded_at BETWEEN ? AND ?
         UNION ALL
         SELECT 
            'comments' as type, SUM(a.comments_count) as count
         FROM analytics a
         JOIN posts p ON a.post_id = p.id
         JOIN social_accounts sa ON a.social_account_id = sa.id
         WHERE {$filter['where']} AND a.recorded_at BETWEEN ? AND ?
         UNION ALL
         SELECT 
            'shares' as type, SUM(a.shares_count) as count
         FROM analytics a
         JOIN posts p ON a.post_id = p.id
         JOIN social_accounts sa ON a.social_account_id = sa.id
         WHERE {$filter['where']} AND a.recorded_at BETWEEN ? AND ?",
        array_merge(
            $filter['params'], [$dates['start_date'], $dates['end_date']],
            $filter['params'], [$dates['start_date'], $dates['end_date']],
            $filter['params'], [$dates['start_date'], $dates['end_date']]
        )
    );
    
    // Top publications par engagement
    $topPosts = $db->fetchAll(
        "SELECT 
            p.id, p.title, p.content, p.published_at,
            sa.platform, sa.account_name,
            SUM(a.likes_count + a.comments_count + a.shares_count) as total_engagement,
            AVG(a.engagement_rate) as avg_engagement_rate
         FROM analytics a
         JOIN posts p ON a.post_id = p.id
         JOIN social_accounts sa ON a.social_account_id = sa.id
         WHERE {$filter['where']} AND a.recorded_at BETWEEN ? AND ?
         GROUP BY p.id, sa.platform
         ORDER BY total_engagement DESC
         LIMIT 10",
        array_merge($filter['params'], [$dates['start_date'], $dates['end_date']])
    );
    
    return [
        'engagement_by_type' => $engagementByType,
        'top_posts' => $topPosts
    ];
}

/**
 * Statistiques de portée
 */
function getReachStats($db, $userId, $teamId, $platform, $dates) {
    $filter = buildWhereClause($userId, $teamId, $platform);
    
    // Portée par plateforme
    $reachByPlatform = $db->fetchAll(
        "SELECT 
            sa.platform,
            SUM(a.reach_count) as total_reach,
            SUM(a.impressions_count) as total_impressions,
            COUNT(DISTINCT p.id) as posts_count
         FROM analytics a
         JOIN posts p ON a.post_id = p.id
         JOIN social_accounts sa ON a.social_account_id = sa.id
         WHERE {$filter['where']} AND a.recorded_at BETWEEN ? AND ?
         GROUP BY sa.platform
         ORDER BY total_reach DESC",
        array_merge($filter['params'], [$dates['start_date'], $dates['end_date']])
    );
    
    return [
        'reach_by_platform' => $reachByPlatform
    ];
}

/**
 * Statistiques de croissance
 */
function getGrowthStats($db, $userId, $teamId, $platform, $dates) {
    // Évolution des followers (simulation avec des données aléatoires)
    $followersGrowth = [];
    $currentDate = strtotime($dates['start_date']);
    $endDate = strtotime($dates['end_date']);
    
    while ($currentDate <= $endDate) {
        $followersGrowth[] = [
            'date' => date('Y-m-d', $currentDate),
            'followers' => rand(1000, 5000),
            'growth' => rand(-50, 100)
        ];
        $currentDate = strtotime('+1 day', $currentDate);
    }
    
    return [
        'followers_growth' => $followersGrowth
    ];
}

/**
 * Statistiques des publications
 */
function getPostsStats($db, $userId, $teamId, $platform, $dates) {
    $filter = buildWhereClause($userId, $teamId, $platform);
    
    // Publications par statut
    $postsByStatus = $db->fetchAll(
        "SELECT 
            p.status,
            COUNT(*) as count
         FROM posts p
         WHERE p.user_id = ? AND p.created_at BETWEEN ? AND ?
         GROUP BY p.status",
        [$userId, $dates['start_date'], $dates['end_date']]
    );
    
    return [
        'posts_by_status' => $postsByStatus
    ];
}

/**
 * Statistiques par plateforme
 */
function getPlatformsStats($db, $userId, $teamId, $dates) {
    // Performances par plateforme
    $platformStats = $db->fetchAll(
        "SELECT 
            sa.platform,
            COUNT(DISTINCT p.id) as posts_count,
            SUM(a.likes_count + a.comments_count + a.shares_count) as total_engagement,
            AVG(a.engagement_rate) as avg_engagement_rate,
            SUM(a.reach_count) as total_reach
         FROM analytics a
         JOIN posts p ON a.post_id = p.id
         JOIN social_accounts sa ON a.social_account_id = sa.id
         WHERE p.user_id = ? AND a.recorded_at BETWEEN ? AND ?
         GROUP BY sa.platform
         ORDER BY total_engagement DESC",
        [$userId, $dates['start_date'], $dates['end_date']]
    );
    
    return [
        'platform_stats' => $platformStats
    ];
}
?>
