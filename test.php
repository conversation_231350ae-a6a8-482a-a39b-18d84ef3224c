<?php
/**
 * Fichier de test pour vérifier la configuration
 */

// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Test de configuration Social Scheduler</h1>";

// Test 1: Version PHP
echo "<h2>1. Version PHP</h2>";
echo "Version PHP: " . phpversion() . "<br>";
if (version_compare(phpversion(), '7.4.0', '>=')) {
    echo "<span style='color: green;'>✓ Version PHP compatible</span><br>";
} else {
    echo "<span style='color: red;'>✗ Version PHP trop ancienne (minimum 7.4)</span><br>";
}

// Test 2: Extensions PHP
echo "<h2>2. Extensions PHP</h2>";
$required_extensions = ['pdo', 'pdo_mysql', 'json', 'openssl'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<span style='color: green;'>✓ Extension $ext chargée</span><br>";
    } else {
        echo "<span style='color: red;'>✗ Extension $ext manquante</span><br>";
    }
}

// Test 3: Connexion à la base de données
echo "<h2>3. Test de connexion à la base de données</h2>";
try {
    $host = 'localhost';
    $username = 'root';
    $password = '';
    
    // Test de connexion sans base de données spécifique
    $dsn = "mysql:host=$host;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo "<span style='color: green;'>✓ Connexion MySQL réussie</span><br>";
    
    // Vérifier si la base de données existe
    $stmt = $pdo->query("SHOW DATABASES LIKE 'social_scheduler'");
    if ($stmt->rowCount() > 0) {
        echo "<span style='color: green;'>✓ Base de données 'social_scheduler' existe</span><br>";
        
        // Test de connexion à la base de données
        $dsn_with_db = "mysql:host=$host;dbname=social_scheduler;charset=utf8mb4";
        $pdo_db = new PDO($dsn_with_db, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        echo "<span style='color: green;'>✓ Connexion à la base de données réussie</span><br>";
        
        // Vérifier les tables
        $stmt = $pdo_db->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        if (count($tables) > 0) {
            echo "<span style='color: green;'>✓ Tables trouvées: " . implode(', ', $tables) . "</span><br>";
        } else {
            echo "<span style='color: orange;'>⚠ Aucune table trouvée - Vous devez importer le schéma</span><br>";
        }
        
    } else {
        echo "<span style='color: orange;'>⚠ Base de données 'social_scheduler' n'existe pas</span><br>";
        echo "<span style='color: blue;'>ℹ Tentative de création...</span><br>";
        
        try {
            $pdo->exec("CREATE DATABASE social_scheduler CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "<span style='color: green;'>✓ Base de données créée avec succès</span><br>";
        } catch (Exception $e) {
            echo "<span style='color: red;'>✗ Erreur lors de la création: " . $e->getMessage() . "</span><br>";
        }
    }
    
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ Erreur de connexion: " . $e->getMessage() . "</span><br>";
}

// Test 4: Permissions de fichiers
echo "<h2>4. Permissions de fichiers</h2>";
$directories = ['api', 'css', 'js', 'logs'];
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_readable($dir)) {
            echo "<span style='color: green;'>✓ Dossier $dir accessible en lecture</span><br>";
        } else {
            echo "<span style='color: red;'>✗ Dossier $dir non accessible en lecture</span><br>";
        }
        
        if (is_writable($dir)) {
            echo "<span style='color: green;'>✓ Dossier $dir accessible en écriture</span><br>";
        } else {
            echo "<span style='color: orange;'>⚠ Dossier $dir non accessible en écriture</span><br>";
        }
    } else {
        echo "<span style='color: red;'>✗ Dossier $dir n'existe pas</span><br>";
    }
}

// Test 5: Configuration Apache
echo "<h2>5. Configuration serveur</h2>";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    $required_modules = ['mod_rewrite', 'mod_headers'];
    foreach ($required_modules as $module) {
        if (in_array($module, $modules)) {
            echo "<span style='color: green;'>✓ Module Apache $module activé</span><br>";
        } else {
            echo "<span style='color: orange;'>⚠ Module Apache $module non détecté</span><br>";
        }
    }
} else {
    echo "<span style='color: blue;'>ℹ Impossible de vérifier les modules Apache</span><br>";
}

// Test 6: Test de l'API
echo "<h2>6. Test de l'API</h2>";
if (file_exists('api/config/database.php')) {
    echo "<span style='color: green;'>✓ Fichier de configuration API trouvé</span><br>";
    
    // Test d'inclusion
    try {
        require_once 'api/config/database.php';
        echo "<span style='color: green;'>✓ Configuration API chargée sans erreur</span><br>";
        
        // Test de création d'instance
        $db = new Database();
        echo "<span style='color: green;'>✓ Classe Database instanciée</span><br>";
        
        // Test de connexion
        $conn = $db->getConnection();
        if ($conn) {
            echo "<span style='color: green;'>✓ Connexion via classe Database réussie</span><br>";
        }
        
    } catch (Exception $e) {
        echo "<span style='color: red;'>✗ Erreur lors du chargement de l'API: " . $e->getMessage() . "</span><br>";
    }
} else {
    echo "<span style='color: red;'>✗ Fichier de configuration API non trouvé</span><br>";
}

echo "<h2>Instructions</h2>";
echo "<ol>";
echo "<li>Si la base de données n'existe pas, importez le fichier <code>database/schema.sql</code></li>";
echo "<li>Vérifiez que tous les modules Apache requis sont activés</li>";
echo "<li>Assurez-vous que les permissions de fichiers sont correctes</li>";
echo "<li>Si tout est vert, l'application devrait fonctionner !</li>";
echo "</ol>";

echo "<p><a href='index.html'>→ Aller à l'application</a></p>";
?>
