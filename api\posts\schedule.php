<?php
require_once '../config/database.php';
require_once '../config/auth.php';

/**
 * API de planification des publications
 */

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
    exit();
}

try {
    $auth = new Auth();
    $permissions = new Permissions();
    $db = new DatabaseHelper();
    
    // Vérification de l'authentification
    $user = $auth->requireAuth();
    
    // Récupération des données JSON
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Données JSON invalides');
    }
    
    // Validation des champs requis
    $postId = $input['post_id'] ?? null;
    $scheduledAt = $input['scheduled_at'] ?? null;
    $socialAccountIds = $input['social_account_ids'] ?? [];
    
    if (!$postId || !$scheduledAt || empty($socialAccountIds)) {
        throw new Exception('ID de publication, date de planification et comptes sociaux requis');
    }
    
    // Validation de la date
    $scheduledTimestamp = strtotime($scheduledAt);
    if ($scheduledTimestamp === false || $scheduledTimestamp <= time()) {
        throw new Exception('La date de planification doit être dans le futur');
    }
    
    // Vérification que la publication appartient à l'utilisateur
    $post = $db->fetchOne(
        "SELECT p.*, t.owner_id as team_owner_id 
         FROM posts p 
         LEFT JOIN teams t ON p.team_id = t.id 
         WHERE p.id = ?",
        [$postId]
    );
    
    if (!$post) {
        throw new Exception('Publication non trouvée');
    }
    
    // Vérification des permissions
    $canEdit = ($post['user_id'] == $user['id']);
    if ($post['team_id']) {
        $canEdit = $canEdit || $permissions->canEditTeam($user['id'], $post['team_id']);
    }
    
    if (!$canEdit) {
        throw new Exception('Vous n\'avez pas les permissions pour modifier cette publication');
    }
    
    // Validation des comptes sociaux
    $validAccounts = validateSocialAccounts($db, $user['id'], $socialAccountIds);
    if (count($validAccounts) !== count($socialAccountIds)) {
        throw new Exception('Un ou plusieurs comptes sociaux sont invalides');
    }
    
    // Vérification des limites de contenu par plateforme
    $contentValidation = validateContentForPlatforms($post['content'], $validAccounts);
    if (!$contentValidation['valid']) {
        throw new Exception($contentValidation['error']);
    }
    
    // Début de transaction
    $db->beginTransaction();
    
    try {
        // Suppression des anciennes planifications
        $db->delete('scheduled_posts', 'post_id = ?', [$postId]);
        
        // Mise à jour de la publication
        $db->update(
            'posts',
            [
                'status' => 'scheduled',
                'scheduled_at' => $scheduledAt,
                'updated_at' => date('Y-m-d H:i:s')
            ],
            'id = ?',
            [$postId]
        );
        
        // Création des nouvelles planifications
        $scheduledPostIds = [];
        foreach ($socialAccountIds as $accountId) {
            $scheduledPostId = $db->insert('scheduled_posts', [
                'post_id' => $postId,
                'social_account_id' => $accountId,
                'status' => 'pending',
                'scheduled_at' => $scheduledAt
            ]);
            $scheduledPostIds[] = $scheduledPostId;
        }
        
        // Validation de la transaction
        $db->commit();
        
        // Récupération de la publication mise à jour
        $updatedPost = getPostWithSchedules($db, $postId);
        
        // Programmation de la tâche de publication (simulation)
        schedulePublicationTask($postId, $scheduledAt);
        
        // Log de planification
        error_log("Publication planifiée par {$user['email']} : ID {$postId} pour {$scheduledAt}");
        
        // Réponse de succès
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => 'Publication planifiée avec succès',
            'data' => [
                'post' => $updatedPost,
                'scheduled_post_ids' => $scheduledPostIds
            ]
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Valide les comptes sociaux de l'utilisateur
 */
function validateSocialAccounts($db, $userId, $accountIds) {
    if (empty($accountIds)) {
        return [];
    }
    
    $placeholders = str_repeat('?,', count($accountIds) - 1) . '?';
    $params = array_merge([$userId], $accountIds);
    
    return $db->fetchAll(
        "SELECT id, platform, account_name, access_token FROM social_accounts 
         WHERE user_id = ? AND id IN ({$placeholders}) AND is_active = 1",
        $params
    );
}

/**
 * Valide le contenu pour chaque plateforme
 */
function validateContentForPlatforms($content, $accounts) {
    $platformLimits = [
        'twitter' => 280,
        'facebook' => 63206,
        'instagram' => 2200,
        'linkedin' => 3000,
        'tiktok' => 2200,
        'youtube' => 5000,
        'pinterest' => 500,
        'reddit' => 40000
    ];
    
    foreach ($accounts as $account) {
        $platform = $account['platform'];
        $limit = $platformLimits[$platform] ?? 2000;
        
        if (strlen($content) > $limit) {
            return [
                'valid' => false,
                'error' => "Le contenu dépasse la limite de {$limit} caractères pour {$platform}"
            ];
        }
        
        // Vérifications spécifiques par plateforme
        switch ($platform) {
            case 'twitter':
                // Twitter a des règles spéciales pour les hashtags et mentions
                if (preg_match_all('/#\w+/', $content) > 10) {
                    return [
                        'valid' => false,
                        'error' => 'Twitter limite à 10 hashtags maximum'
                    ];
                }
                break;
                
            case 'instagram':
                // Instagram permet jusqu'à 30 hashtags
                if (preg_match_all('/#\w+/', $content) > 30) {
                    return [
                        'valid' => false,
                        'error' => 'Instagram limite à 30 hashtags maximum'
                    ];
                }
                break;
        }
    }
    
    return ['valid' => true];
}

/**
 * Récupère une publication avec ses planifications
 */
function getPostWithSchedules($db, $postId) {
    $post = $db->fetchOne(
        "SELECT p.*, u.username, u.first_name, u.last_name,
                t.name as team_name
         FROM posts p
         JOIN users u ON p.user_id = u.id
         LEFT JOIN teams t ON p.team_id = t.id
         WHERE p.id = ?",
        [$postId]
    );
    
    if (!$post) {
        return null;
    }
    
    // Décodage des JSON
    $post['media_urls'] = json_decode($post['media_urls'] ?? '[]', true);
    $post['hashtags'] = json_decode($post['hashtags'] ?? '[]', true);
    
    // Planifications
    $post['scheduled_accounts'] = $db->fetchAll(
        "SELECT sp.*, sa.platform, sa.account_name, sa.profile_picture
         FROM scheduled_posts sp
         JOIN social_accounts sa ON sp.social_account_id = sa.id
         WHERE sp.post_id = ?
         ORDER BY sa.platform",
        [$postId]
    );
    
    return $post;
}

/**
 * Programme la tâche de publication (simulation)
 */
function schedulePublicationTask($postId, $scheduledAt) {
    // Dans un vrai projet, vous utiliseriez un système de queue comme Redis/RabbitMQ
    // ou un cron job pour traiter les publications planifiées
    
    $taskData = [
        'post_id' => $postId,
        'scheduled_at' => $scheduledAt,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    // Simulation : écriture dans un fichier de log
    $logFile = __DIR__ . '/../../logs/scheduled_tasks.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents(
        $logFile,
        json_encode($taskData) . "\n",
        FILE_APPEND | LOCK_EX
    );
    
    error_log("Tâche de publication programmée : " . json_encode($taskData));
}
?>
