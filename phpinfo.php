<?php
// Test simple pour vérifier que PHP fonctionne
echo "<h1>Test PHP - Social Scheduler</h1>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Date/Heure: " . date('Y-m-d H:i:s') . "</p>";

// Test des extensions
echo "<h2>Extensions PHP:</h2>";
$extensions = ['pdo', 'pdo_mysql', 'json', 'openssl'];
foreach ($extensions as $ext) {
    $status = extension_loaded($ext) ? '✓ Chargée' : '✗ Manquante';
    $color = extension_loaded($ext) ? 'green' : 'red';
    echo "<p style='color: $color;'>$ext: $status</p>";
}

// Test de connexion MySQL simple
echo "<h2>Test MySQL:</h2>";
try {
    $pdo = new PDO('mysql:host=localhost', 'root', '');
    echo "<p style='color: green;'>✓ Connexion MySQL réussie</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Erreur MySQL: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='install.php'>→ Aller à l'installation</a></p>";
echo "<p><a href='index.html'>→ Aller à l'application</a></p>";
?>
