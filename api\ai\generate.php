<?php
require_once '../config/database.php';
require_once '../config/auth.php';

/**
 * API de génération de contenu IA
 */

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
    exit();
}

try {
    $auth = new Auth();
    $db = new DatabaseHelper();
    
    // Vérification de l'authentification
    $user = $auth->requireAuth();
    
    // Récupération des données JSON
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Données JSON invalides');
    }
    
    $type = $input['type'] ?? 'post'; // post, hashtags, caption, ideas
    $prompt = $input['prompt'] ?? '';
    $platform = $input['platform'] ?? 'general';
    $tone = $input['tone'] ?? 'professional'; // professional, casual, funny, inspiring
    $length = $input['length'] ?? 'medium'; // short, medium, long
    $language = $input['language'] ?? 'fr';
    
    // Validation des paramètres
    $validTypes = ['post', 'hashtags', 'caption', 'ideas', 'improve'];
    if (!in_array($type, $validTypes)) {
        throw new Exception('Type de génération non supporté');
    }
    
    $validPlatforms = ['general', 'facebook', 'instagram', 'twitter', 'linkedin', 'tiktok'];
    if (!in_array($platform, $validPlatforms)) {
        $platform = 'general';
    }
    
    $validTones = ['professional', 'casual', 'funny', 'inspiring', 'educational'];
    if (!in_array($tone, $validTones)) {
        $tone = 'professional';
    }
    
    // Génération du contenu selon le type
    $result = [];
    
    switch ($type) {
        case 'post':
            $result = generatePost($prompt, $platform, $tone, $length, $language);
            break;
            
        case 'hashtags':
            $result = generateHashtags($prompt, $platform, $language);
            break;
            
        case 'caption':
            $result = generateCaption($prompt, $platform, $tone, $language);
            break;
            
        case 'ideas':
            $result = generateIdeas($prompt, $platform, $language);
            break;
            
        case 'improve':
            $result = improveContent($prompt, $platform, $tone, $language);
            break;
    }
    
    // Sauvegarde de l'historique de génération
    saveGenerationHistory($db, $user['id'], $type, $prompt, $result);
    
    // Réponse de succès
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'data' => $result,
        'usage' => [
            'type' => $type,
            'platform' => $platform,
            'tone' => $tone,
            'length' => $length,
            'language' => $language
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Génère un post complet
 */
function generatePost($prompt, $platform, $tone, $length, $language) {
    // Simulation de génération IA (remplacez par un vrai appel à OpenAI)
    $templates = [
        'professional' => [
            'short' => "🔥 {prompt}\n\nDécouvrez comment cette innovation peut transformer votre approche.\n\n#Innovation #Business",
            'medium' => "💡 {prompt}\n\nDans un monde en constante évolution, il est essentiel de rester à la pointe des tendances. Cette approche révolutionnaire offre de nouvelles perspectives pour optimiser vos résultats.\n\nQue pensez-vous de cette stratégie ?\n\n#Innovation #Stratégie #Business",
            'long' => "🚀 {prompt}\n\nL'innovation est au cœur de toute stratégie réussie. En adoptant cette nouvelle approche, les entreprises peuvent non seulement améliorer leur efficacité, mais aussi créer de nouvelles opportunités de croissance.\n\nLes avantages incluent :\n✅ Amélioration de la productivité\n✅ Réduction des coûts\n✅ Satisfaction client accrue\n\nComment votre entreprise s'adapte-t-elle aux nouvelles tendances ?\n\n#Innovation #Transformation #Business #Croissance"
        ],
        'casual' => [
            'short' => "Hey ! 👋 {prompt}\n\nÇa vous dit quelque chose ? 🤔\n\n#Lifestyle #Partage",
            'medium' => "Salut tout le monde ! 😊\n\n{prompt}\n\nJ'ai découvert ça récemment et je trouve ça vraiment intéressant ! Qu'est-ce que vous en pensez ?\n\nDites-moi en commentaire ! 👇\n\n#Découverte #Partage #Communauté",
            'long' => "Hello la communauté ! 🌟\n\n{prompt}\n\nJe voulais partager avec vous cette découverte qui m'a vraiment marqué. C'est fou comme parfois les petites choses peuvent avoir un grand impact sur notre quotidien.\n\nPersonnellement, ça m'a fait réfléchir sur :\n🤔 Notre façon de voir les choses\n💭 L'importance de rester ouvert\n✨ Les opportunités qui nous entourent\n\nEt vous, qu'est-ce qui vous a marqué récemment ?\n\n#Réflexion #Partage #Communauté #Inspiration"
        ]
    ];
    
    $toneTemplates = $templates[$tone] ?? $templates['professional'];
    $template = $toneTemplates[$length] ?? $toneTemplates['medium'];
    
    $content = str_replace('{prompt}', $prompt, $template);
    
    // Adaptation par plateforme
    $content = adaptContentForPlatform($content, $platform);
    
    return [
        'content' => $content,
        'character_count' => strlen($content),
        'estimated_reading_time' => ceil(str_word_count($content) / 200), // mots par minute
        'suggestions' => [
            'Ajoutez une image pour plus d\'engagement',
            'Posez une question pour encourager les commentaires',
            'Utilisez des emojis pour rendre le contenu plus attrayant'
        ]
    ];
}

/**
 * Génère des hashtags pertinents
 */
function generateHashtags($prompt, $platform, $language) {
    // Base de hashtags par catégorie
    $hashtagCategories = [
        'business' => ['#Business', '#Entrepreneur', '#Innovation', '#Stratégie', '#Croissance', '#Leadership'],
        'tech' => ['#Tech', '#Innovation', '#Digital', '#IA', '#Technologie', '#Futur'],
        'lifestyle' => ['#Lifestyle', '#Inspiration', '#Motivation', '#Bien-être', '#Quotidien', '#Partage'],
        'marketing' => ['#Marketing', '#SocialMedia', '#Contenu', '#Branding', '#Communication', '#Digital']
    ];
    
    // Analyse du prompt pour déterminer la catégorie
    $category = 'business'; // Par défaut
    if (stripos($prompt, 'tech') !== false || stripos($prompt, 'technologie') !== false) {
        $category = 'tech';
    } elseif (stripos($prompt, 'lifestyle') !== false || stripos($prompt, 'vie') !== false) {
        $category = 'lifestyle';
    } elseif (stripos($prompt, 'marketing') !== false || stripos($prompt, 'communication') !== false) {
        $category = 'marketing';
    }
    
    $hashtags = $hashtagCategories[$category];
    
    // Hashtags spécifiques au prompt
    $words = explode(' ', $prompt);
    foreach ($words as $word) {
        if (strlen($word) > 4) {
            $hashtags[] = '#' . ucfirst(strtolower($word));
        }
    }
    
    // Limitation selon la plateforme
    $maxHashtags = [
        'instagram' => 30,
        'twitter' => 10,
        'linkedin' => 10,
        'facebook' => 10,
        'tiktok' => 20
    ];
    
    $limit = $maxHashtags[$platform] ?? 15;
    $hashtags = array_slice(array_unique($hashtags), 0, $limit);
    
    return [
        'hashtags' => $hashtags,
        'count' => count($hashtags),
        'platform_limit' => $limit,
        'formatted' => implode(' ', $hashtags)
    ];
}

/**
 * Génère une légende pour une image
 */
function generateCaption($prompt, $platform, $tone, $language) {
    $captions = [
        'professional' => "✨ {prompt}\n\nUne image qui en dit long sur l'excellence et l'innovation.",
        'casual' => "😍 {prompt}\n\nJ'adore cette vibe ! Et vous ?",
        'funny' => "😂 {prompt}\n\nQuand tu réalises que... 🤷‍♀️",
        'inspiring' => "🌟 {prompt}\n\nChaque moment est une opportunité de grandir."
    ];
    
    $caption = str_replace('{prompt}', $prompt, $captions[$tone] ?? $captions['professional']);
    
    return [
        'caption' => $caption,
        'character_count' => strlen($caption),
        'call_to_action' => 'Dites-nous ce que vous en pensez en commentaire ! 👇'
    ];
}

/**
 * Génère des idées de contenu
 */
function generateIdeas($prompt, $platform, $language) {
    $ideas = [
        "Créez un tutoriel étape par étape sur {prompt}",
        "Partagez votre expérience personnelle avec {prompt}",
        "Faites une comparaison avant/après concernant {prompt}",
        "Organisez un Q&A sur le thème de {prompt}",
        "Créez une série de posts sur les aspects de {prompt}",
        "Partagez des conseils pratiques liés à {prompt}",
        "Montrez les coulisses de votre travail sur {prompt}",
        "Créez un sondage pour connaître l'opinion sur {prompt}",
        "Partagez une citation inspirante en lien avec {prompt}",
        "Faites un récapitulatif des tendances autour de {prompt}"
    ];
    
    $generatedIdeas = [];
    foreach (array_slice($ideas, 0, 5) as $idea) {
        $generatedIdeas[] = str_replace('{prompt}', $prompt, $idea);
    }
    
    return [
        'ideas' => $generatedIdeas,
        'count' => count($generatedIdeas),
        'category' => 'Idées de contenu'
    ];
}

/**
 * Améliore un contenu existant
 */
function improveContent($content, $platform, $tone, $language) {
    // Analyse du contenu
    $wordCount = str_word_count($content);
    $charCount = strlen($content);
    $hasHashtags = strpos($content, '#') !== false;
    $hasEmojis = preg_match('/[\x{1F600}-\x{1F64F}]|[\x{1F300}-\x{1F5FF}]|[\x{1F680}-\x{1F6FF}]|[\x{1F1E0}-\x{1F1FF}]/u', $content);
    
    $improvements = [];
    $improvedContent = $content;
    
    // Suggestions d'amélioration
    if (!$hasEmojis) {
        $improvements[] = "Ajout d'emojis pour plus d'engagement";
        $improvedContent = "✨ " . $improvedContent;
    }
    
    if (!$hasHashtags) {
        $improvements[] = "Ajout de hashtags pertinents";
        $hashtagResult = generateHashtags($content, $platform, $language);
        $improvedContent .= "\n\n" . implode(' ', array_slice($hashtagResult['hashtags'], 0, 5));
    }
    
    if ($wordCount < 10) {
        $improvements[] = "Développement du contenu pour plus d'impact";
    }
    
    if (!preg_match('/[?!]/', $content)) {
        $improvements[] = "Ajout d'une question pour encourager l'interaction";
        $improvedContent .= "\n\nQu'en pensez-vous ? 🤔";
    }
    
    return [
        'original_content' => $content,
        'improved_content' => $improvedContent,
        'improvements' => $improvements,
        'analysis' => [
            'word_count' => $wordCount,
            'character_count' => $charCount,
            'has_hashtags' => $hasHashtags,
            'has_emojis' => $hasEmojis
        ]
    ];
}

/**
 * Adapte le contenu selon la plateforme
 */
function adaptContentForPlatform($content, $platform) {
    switch ($platform) {
        case 'twitter':
            // Limitation à 280 caractères
            if (strlen($content) > 280) {
                $content = substr($content, 0, 277) . '...';
            }
            break;
            
        case 'linkedin':
            // Style plus professionnel
            $content = str_replace('Hey !', 'Bonjour,', $content);
            break;
            
        case 'instagram':
            // Plus d'emojis et de hashtags
            if (strpos($content, '📸') === false) {
                $content = "📸 " . $content;
            }
            break;
    }
    
    return $content;
}

/**
 * Sauvegarde l'historique de génération
 */
function saveGenerationHistory($db, $userId, $type, $prompt, $result) {
    try {
        // Création d'une table pour l'historique si elle n'existe pas
        $db->executeQuery("
            CREATE TABLE IF NOT EXISTS ai_generation_history (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                type VARCHAR(50) NOT NULL,
                prompt TEXT,
                result JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ");
        
        $db->insert('ai_generation_history', [
            'user_id' => $userId,
            'type' => $type,
            'prompt' => $prompt,
            'result' => json_encode($result)
        ]);
    } catch (Exception $e) {
        // Log l'erreur mais ne fait pas échouer la requête principale
        error_log("Erreur sauvegarde historique IA : " . $e->getMessage());
    }
}
?>
