<?php
require_once '../config/database.php';
require_once '../config/auth.php';

/**
 * API simplifiée de création de publications
 */

// Headers CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=UTF-8');

// Gestion des requêtes OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Méthode non autorisée']);
    exit();
}

try {
    // Vérifier l'authentification
    $auth = new Auth();
    $user = $auth->getCurrentUser();
    
    if (!$user) {
        throw new Exception('Authentification requise');
    }

    $database = new Database();
    $db = $database->getConnection();
    
    // Récupération des données JSON
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Données JSON invalides');
    }
    
    // Validation des champs requis
    if (empty($input['content'])) {
        throw new Exception('Le contenu est requis');
    }
    
    // Extraction des données
    $title = $input['title'] ?? null;
    $content = $input['content'];
    $hashtags = $input['hashtags'] ?? [];
    $status = $input['status'] ?? 'draft';
    $scheduledAt = $input['scheduled_at'] ?? null;
    $platforms = $input['platforms'] ?? [];
    
    // Validation du statut
    $validStatuses = ['draft', 'scheduled'];
    if (!in_array($status, $validStatuses)) {
        throw new Exception('Statut invalide');
    }
    
    // Si planifié, la date est requise
    if ($status === 'scheduled' && empty($scheduledAt)) {
        throw new Exception('Date de planification requise pour les publications planifiées');
    }
    
    // Validation de la date de planification
    if ($scheduledAt) {
        $scheduledDate = strtotime($scheduledAt);
        if ($scheduledDate === false || $scheduledDate <= time()) {
            throw new Exception('La date de planification doit être dans le futur');
        }
        $status = 'scheduled';
    }
    
    // Traitement des hashtags
    if (is_string($hashtags)) {
        $hashtags = array_filter(array_map('trim', explode(' ', $hashtags)));
        $hashtags = array_map(function($tag) {
            return strpos($tag, '#') === 0 ? $tag : '#' . $tag;
        }, $hashtags);
    }
    
    // Début de la transaction
    $db->beginTransaction();
    
    try {
        // Création de la publication
        $stmt = $db->prepare("
            INSERT INTO posts (user_id, title, content, hashtags, status, scheduled_at, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $user['id'],
            $title,
            $content,
            json_encode($hashtags),
            $status,
            $scheduledAt ? date('Y-m-d H:i:s', strtotime($scheduledAt)) : null
        ]);
        
        $postId = $db->lastInsertId();
        
        // Planification sur les plateformes sélectionnées
        if (!empty($platforms)) {
            foreach ($platforms as $platformId) {
                // Vérifier que la plateforme appartient à l'utilisateur
                $stmt = $db->prepare("
                    SELECT id FROM social_accounts 
                    WHERE id = ? AND user_id = ? AND is_active = 1
                ");
                $stmt->execute([$platformId, $user['id']]);
                $account = $stmt->fetch();
                
                if ($account) {
                    $stmt = $db->prepare("
                        INSERT INTO scheduled_posts (post_id, social_account_id, status, scheduled_at, created_at) 
                        VALUES (?, ?, ?, ?, NOW())
                    ");
                    $stmt->execute([
                        $postId,
                        $platformId,
                        $status === 'scheduled' ? 'pending' : 'draft',
                        $scheduledAt ? date('Y-m-d H:i:s', strtotime($scheduledAt)) : null
                    ]);
                }
            }
        }
        
        // Validation et commit
        $db->commit();
        
        // Récupération de la publication créée
        $stmt = $db->prepare("
            SELECT p.*, 
                   GROUP_CONCAT(sa.platform) as platforms,
                   COUNT(sp.id) as scheduled_accounts_count
            FROM posts p
            LEFT JOIN scheduled_posts sp ON p.id = sp.post_id
            LEFT JOIN social_accounts sa ON sp.social_account_id = sa.id
            WHERE p.id = ?
            GROUP BY p.id
        ");
        $stmt->execute([$postId]);
        $post = $stmt->fetch();
        
        if ($post) {
            $post['hashtags'] = json_decode($post['hashtags'] ?? '[]', true);
            $post['platforms'] = $post['platforms'] ? explode(',', $post['platforms']) : [];
        }
        
        http_response_code(201);
        echo json_encode([
            'success' => true,
            'message' => 'Publication créée avec succès',
            'data' => [
                'post' => $post
            ]
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
