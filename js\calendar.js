/**
 * Gestion du calendrier avec FullCalendar
 */

class CalendarManager {
    constructor() {
        this.calendar = null;
        this.events = [];
        this.filters = {
            platform: '',
            status: ''
        };
        this.init();
    }

    async init() {
        // Vérifier l'authentification
        if (!Utils.requireAuth()) {
            return;
        }

        try {
            // Charger les données utilisateur
            await this.loadUserData();
            
            // Initialiser le calendrier
            this.initCalendar();
            
            // Charger les événements
            await this.loadEvents();
            
            // Initialiser l'interface
            this.initUI();
            
        } catch (error) {
            console.error('Calendar initialization error:', error);
            Utils.showAlert('Erreur lors du chargement du calendrier', 'error');
        }
    }

    async loadUserData() {
        const userData = localStorage.getItem('user_data');
        if (userData) {
            this.user = JSON.parse(userData);
            this.updateUserInfo();
        }
    }

    updateUserInfo() {
        if (!this.user) return;

        const userNameEl = document.getElementById('user-name');
        const userPlanEl = document.getElementById('user-plan');
        const userAvatarEl = document.getElementById('user-avatar');

        if (userNameEl) {
            userNameEl.textContent = `${this.user.first_name} ${this.user.last_name}`;
        }

        if (userPlanEl) {
            userPlanEl.textContent = `Plan ${this.user.plan_type.charAt(0).toUpperCase() + this.user.plan_type.slice(1)}`;
        }

        if (userAvatarEl && this.user.avatar_url) {
            userAvatarEl.src = this.user.avatar_url;
        }
    }

    initCalendar() {
        const calendarEl = document.getElementById('calendar');
        if (!calendarEl) return;

        this.calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            locale: 'fr',
            firstDay: 1, // Lundi
            height: 'auto',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: ''
            },
            buttonText: {
                today: 'Aujourd\'hui',
                month: 'Mois',
                week: 'Semaine',
                day: 'Jour'
            },
            events: [],
            eventClick: (info) => this.handleEventClick(info),
            dateClick: (info) => this.handleDateClick(info),
            eventDidMount: (info) => this.handleEventMount(info),
            datesSet: (info) => this.handleDatesSet(info),
            eventClassNames: (arg) => {
                return [`event-${arg.event.extendedProps.status}`];
            }
        });

        this.calendar.render();
    }

    async loadEvents() {
        try {
            const response = await Utils.apiRequest('posts/list.php?status=all&limit=100');
            if (response.success) {
                this.events = this.transformPostsToEvents(response.data.posts);
                this.updateCalendarEvents();
                this.updateStats();
            }
        } catch (error) {
            console.error('Events loading error:', error);
            // Utiliser des données simulées en cas d'erreur
            this.events = this.generateSampleEvents();
            this.updateCalendarEvents();
            this.updateStats();
        }
    }

    transformPostsToEvents(posts) {
        return posts.map(post => {
            const date = post.scheduled_at || post.created_at;
            const platforms = post.scheduled_accounts?.map(acc => acc.platform) || [];
            
            return {
                id: post.id,
                title: post.title || Utils.truncateText(post.content, 30),
                start: date,
                allDay: false,
                backgroundColor: this.getStatusColor(post.status),
                borderColor: this.getStatusColor(post.status),
                textColor: '#fff',
                extendedProps: {
                    status: post.status,
                    content: post.content,
                    platforms: platforms,
                    post: post
                }
            };
        });
    }

    generateSampleEvents() {
        const events = [];
        const today = new Date();
        const statuses = ['scheduled', 'published', 'failed', 'draft'];
        const platforms = ['facebook', 'instagram', 'twitter', 'linkedin'];
        
        for (let i = 0; i < 20; i++) {
            const date = new Date(today);
            date.setDate(date.getDate() + Math.floor(Math.random() * 60) - 30);
            
            const status = statuses[Math.floor(Math.random() * statuses.length)];
            const platform = platforms[Math.floor(Math.random() * platforms.length)];
            
            events.push({
                id: i + 1,
                title: `Publication ${i + 1}`,
                start: date.toISOString(),
                allDay: false,
                backgroundColor: this.getStatusColor(status),
                borderColor: this.getStatusColor(status),
                textColor: '#fff',
                extendedProps: {
                    status: status,
                    content: `Contenu de la publication ${i + 1}`,
                    platforms: [platform],
                    post: {
                        id: i + 1,
                        title: `Publication ${i + 1}`,
                        content: `Contenu de la publication ${i + 1}`,
                        status: status
                    }
                }
            });
        }
        
        return events;
    }

    getStatusColor(status) {
        const colors = {
            scheduled: '#f59e0b',
            published: '#10b981',
            failed: '#ef4444',
            draft: '#6b7280'
        };
        return colors[status] || colors.draft;
    }

    updateCalendarEvents() {
        if (!this.calendar) return;
        
        const filteredEvents = this.filterEvents();
        this.calendar.removeAllEvents();
        this.calendar.addEventSource(filteredEvents);
    }

    filterEvents() {
        return this.events.filter(event => {
            // Filtre par plateforme
            if (this.filters.platform) {
                if (!event.extendedProps.platforms.includes(this.filters.platform)) {
                    return false;
                }
            }
            
            // Filtre par statut
            if (this.filters.status) {
                if (event.extendedProps.status !== this.filters.status) {
                    return false;
                }
            }
            
            // Filtre par plateformes cochées
            const checkedPlatforms = this.getCheckedPlatforms();
            if (checkedPlatforms.length > 0) {
                const hasCheckedPlatform = event.extendedProps.platforms.some(
                    platform => checkedPlatforms.includes(platform)
                );
                if (!hasCheckedPlatform) {
                    return false;
                }
            }
            
            return true;
        });
    }

    getCheckedPlatforms() {
        const checkboxes = document.querySelectorAll('.platform-checkbox input[type="checkbox"]:checked');
        return Array.from(checkboxes).map(cb => cb.dataset.platform);
    }

    handleEventClick(info) {
        const event = info.event;
        const post = event.extendedProps.post;
        this.showEventDetails(post);
    }

    handleDateClick(info) {
        // Créer une nouvelle publication pour cette date
        Utils.showAlert(`Créer une publication pour le ${Utils.formatDate(info.date)}`, 'info');
    }

    handleEventMount(info) {
        // Ajouter des tooltips ou autres améliorations visuelles
        const platforms = info.event.extendedProps.platforms;
        if (platforms.length > 0) {
            info.el.title = `Plateformes: ${platforms.join(', ')}`;
        }
    }

    handleDatesSet(info) {
        // Mettre à jour les statistiques pour la période visible
        this.updateStatsForPeriod(info.start, info.end);
    }

    showEventDetails(post) {
        const modal = document.getElementById('event-modal');
        const detailsContainer = document.getElementById('event-details');
        
        if (!modal || !detailsContainer) return;
        
        const platforms = post.scheduled_accounts?.map(acc => acc.platform) || [];
        const platformIcons = platforms.map(platform => {
            const iconClass = this.getPlatformIcon(platform);
            return `<i class="${iconClass}" title="${platform}"></i>`;
        }).join(' ');
        
        detailsContainer.innerHTML = `
            <div class="event-detail-content">
                <div class="event-header">
                    <h4>${Utils.escapeHtml(post.title || 'Sans titre')}</h4>
                    <span class="event-status status-${post.status}">${this.getStatusText(post.status)}</span>
                </div>
                
                <div class="event-body">
                    <div class="event-field">
                        <label>Contenu:</label>
                        <div class="event-content">${Utils.escapeHtml(post.content)}</div>
                    </div>
                    
                    <div class="event-field">
                        <label>Plateformes:</label>
                        <div class="event-platforms">${platformIcons || 'Aucune plateforme'}</div>
                    </div>
                    
                    <div class="event-field">
                        <label>Date de planification:</label>
                        <div class="event-date">${Utils.formatDate(post.scheduled_at || post.created_at)}</div>
                    </div>
                    
                    ${post.hashtags && post.hashtags.length > 0 ? `
                        <div class="event-field">
                            <label>Hashtags:</label>
                            <div class="event-hashtags">${post.hashtags.join(' ')}</div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
        
        modal.style.display = 'block';
        this.currentEvent = post;
    }

    getPlatformIcon(platform) {
        const icons = {
            facebook: 'fab fa-facebook',
            instagram: 'fab fa-instagram',
            twitter: 'fab fa-twitter',
            linkedin: 'fab fa-linkedin',
            tiktok: 'fab fa-tiktok',
            youtube: 'fab fa-youtube',
            pinterest: 'fab fa-pinterest',
            reddit: 'fab fa-reddit'
        };
        return icons[platform] || 'fas fa-share-alt';
    }

    getStatusText(status) {
        const texts = {
            draft: 'Brouillon',
            scheduled: 'Planifié',
            published: 'Publié',
            failed: 'Échec'
        };
        return texts[status] || 'Inconnu';
    }

    updateStats() {
        const stats = this.calculateStats();
        
        const monthScheduled = document.getElementById('month-scheduled');
        const monthPublished = document.getElementById('month-published');
        const monthFailed = document.getElementById('month-failed');
        
        if (monthScheduled) monthScheduled.textContent = stats.scheduled;
        if (monthPublished) monthPublished.textContent = stats.published;
        if (monthFailed) monthFailed.textContent = stats.failed;
    }

    calculateStats() {
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        
        const monthEvents = this.events.filter(event => {
            const eventDate = new Date(event.start);
            return eventDate >= startOfMonth && eventDate <= endOfMonth;
        });
        
        return {
            scheduled: monthEvents.filter(e => e.extendedProps.status === 'scheduled').length,
            published: monthEvents.filter(e => e.extendedProps.status === 'published').length,
            failed: monthEvents.filter(e => e.extendedProps.status === 'failed').length
        };
    }

    updateStatsForPeriod(start, end) {
        // Mettre à jour les statistiques pour une période spécifique
        // Cette méthode peut être étendue pour afficher des stats dynamiques
    }

    initUI() {
        // Gestionnaires d'événements pour les boutons de vue
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.target.dataset.view;
                if (view && this.calendar) {
                    this.calendar.changeView(view);
                    
                    // Mettre à jour l'état actif
                    document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                }
            });
        });

        // Gestionnaires pour les filtres
        const platformFilter = document.getElementById('platform-filter');
        const statusFilter = document.getElementById('status-filter');
        
        if (platformFilter) {
            platformFilter.addEventListener('change', (e) => {
                this.filters.platform = e.target.value;
                this.updateCalendarEvents();
            });
        }
        
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.updateCalendarEvents();
            });
        }

        // Gestionnaires pour les checkboxes de plateformes
        document.querySelectorAll('.platform-checkbox input').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateCalendarEvents();
            });
        });

        // Fermeture du modal en cliquant à l'extérieur
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('event-modal');
            if (e.target === modal) {
                this.closeEventModal();
            }
        });
    }

    closeEventModal() {
        const modal = document.getElementById('event-modal');
        if (modal) {
            modal.style.display = 'none';
        }
        this.currentEvent = null;
    }

    editEvent() {
        if (this.currentEvent) {
            Utils.showAlert(`Modification de la publication ${this.currentEvent.id} en cours de développement`, 'info');
            this.closeEventModal();
        }
    }
}

// Fonctions globales
function closeEventModal() {
    if (window.calendarManager) {
        window.calendarManager.closeEventModal();
    }
}

function editEvent() {
    if (window.calendarManager) {
        window.calendarManager.editEvent();
    }
}

function showCreatePostModal() {
    Utils.showAlert('Création de publication en cours de développement', 'info');
}

function showBulkScheduler() {
    Utils.showAlert('Planification en lot en cours de développement', 'info');
}

function exportCalendar() {
    Utils.showAlert('Export du calendrier en cours de développement', 'info');
}

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    window.calendarManager = new CalendarManager();
});

// Styles CSS additionnels pour le calendrier
if (!document.getElementById('calendar-styles')) {
    const style = document.createElement('style');
    style.id = 'calendar-styles';
    style.textContent = `
        .calendar-content {
            display: flex;
            gap: 1.5rem;
            padding: 1.5rem;
            height: calc(100vh - 120px);
        }
        
        .calendar-sidebar {
            width: 280px;
            background: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            overflow-y: auto;
        }
        
        .calendar-main {
            flex: 1;
            background: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
        }
        
        .sidebar-section {
            margin-bottom: 2rem;
        }
        
        .sidebar-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
        }
        
        .legend-item, .platform-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
        }
        
        .legend-color {
            width: 1rem;
            height: 1rem;
            border-radius: 0.25rem;
        }
        
        .legend-color.status-scheduled { background: #f59e0b; }
        .legend-color.status-published { background: #10b981; }
        .legend-color.status-failed { background: #ef4444; }
        .legend-color.status-draft { background: #6b7280; }
        
        .platform-checkbox {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            font-size: 0.875rem;
        }
        
        .platform-icon {
            color: #3b82f6;
        }
        
        .stats-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 1rem;
            background: #f9fafb;
            border-radius: 0.5rem;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #111827;
        }
        
        .stat-label {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }
        
        .quick-actions {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .quick-action-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.2s;
        }
        
        .quick-action-btn:hover {
            background: #f3f4f6;
            border-color: #d1d5db;
        }
        
        .calendar-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .view-selector {
            display: flex;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .view-btn {
            padding: 0.5rem 1rem;
            background: white;
            border: none;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.2s;
        }
        
        .view-btn:hover {
            background: #f9fafb;
        }
        
        .view-btn.active {
            background: #3b82f6;
            color: white;
        }
        
        .calendar-filters {
            display: flex;
            gap: 0.5rem;
        }
        
        .filter-select {
            padding: 0.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            font-size: 0.875rem;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: white;
            border-radius: 0.5rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .modal-title {
            margin: 0;
            font-size: 1.125rem;
            font-weight: 600;
        }
        
        .modal-close {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.25rem;
        }
        
        .modal-body {
            padding: 1.5rem;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
            padding: 1.5rem;
            border-top: 1px solid #e5e7eb;
        }
        
        .event-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .event-status {
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .event-field {
            margin-bottom: 1rem;
        }
        
        .event-field label {
            display: block;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #374151;
        }
        
        .event-content {
            background: #f9fafb;
            padding: 0.75rem;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
        }
        
        .event-platforms i {
            margin-right: 0.5rem;
            font-size: 1.25rem;
            color: #3b82f6;
        }
        
        .fc-event {
            border: none !important;
            font-size: 0.75rem;
        }
        
        .fc-event-title {
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .calendar-content {
                flex-direction: column;
                height: auto;
            }
            
            .calendar-sidebar {
                width: 100%;
                order: 2;
            }
            
            .calendar-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 0.5rem;
            }
            
            .view-selector {
                justify-content: center;
            }
        }
    `;
    document.head.appendChild(style);
}
