<?php
/**
 * Script d'import manuel du schéma SQL
 * À utiliser si l'installateur automatique ne fonctionne pas
 */

// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Import manuel du schéma SQL</h1>";

try {
    // Configuration de la base de données
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $dbname = 'social_scheduler';
    
    echo "<h2>1. Connexion à MySQL</h2>";
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo "<p style='color: green;'>✓ Connexion MySQL réussie</p>";
    
    echo "<h2>2. Création de la base de données</h2>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✓ Base de données '$dbname' créée</p>";
    
    echo "<h2>3. Connexion à la base de données</h2>";
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo "<p style='color: green;'>✓ Connexion à la base de données réussie</p>";
    
    echo "<h2>4. Création des tables</h2>";
    
    // Table users
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            first_name VARCHAR(50),
            last_name VARCHAR(50),
            avatar_url VARCHAR(255),
            timezone VARCHAR(50) DEFAULT 'UTC',
            language VARCHAR(10) DEFAULT 'fr',
            plan_type ENUM('free', 'pro', 'enterprise') DEFAULT 'free',
            is_active BOOLEAN DEFAULT TRUE,
            email_verified BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✓ Table 'users' créée</p>";
    
    // Table teams
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS teams (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            owner_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✓ Table 'teams' créée</p>";
    
    // Table team_members
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS team_members (
            id INT AUTO_INCREMENT PRIMARY KEY,
            team_id INT NOT NULL,
            user_id INT NOT NULL,
            role ENUM('admin', 'editor', 'viewer') DEFAULT 'editor',
            joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_team_user (team_id, user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✓ Table 'team_members' créée</p>";
    
    // Table social_accounts
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS social_accounts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            platform ENUM('facebook', 'instagram', 'twitter', 'linkedin', 'tiktok', 'youtube', 'pinterest', 'reddit') NOT NULL,
            account_name VARCHAR(100) NOT NULL,
            account_id VARCHAR(100) NOT NULL,
            access_token TEXT,
            refresh_token TEXT,
            token_expires_at TIMESTAMP NULL,
            profile_picture VARCHAR(255),
            followers_count INT DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✓ Table 'social_accounts' créée</p>";
    
    // Table posts
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS posts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            team_id INT NULL,
            title VARCHAR(255),
            content TEXT NOT NULL,
            media_urls JSON,
            hashtags JSON,
            status ENUM('draft', 'scheduled', 'published', 'failed') DEFAULT 'draft',
            scheduled_at TIMESTAMP NULL,
            published_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✓ Table 'posts' créée</p>";
    
    // Table scheduled_posts
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS scheduled_posts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            post_id INT NOT NULL,
            social_account_id INT NOT NULL,
            platform_post_id VARCHAR(255),
            status ENUM('pending', 'published', 'failed') DEFAULT 'pending',
            error_message TEXT,
            scheduled_at TIMESTAMP NOT NULL,
            published_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
            FOREIGN KEY (social_account_id) REFERENCES social_accounts(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✓ Table 'scheduled_posts' créée</p>";
    
    // Table analytics
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS analytics (
            id INT AUTO_INCREMENT PRIMARY KEY,
            post_id INT NOT NULL,
            social_account_id INT NOT NULL,
            platform VARCHAR(50) NOT NULL,
            likes_count INT DEFAULT 0,
            comments_count INT DEFAULT 0,
            shares_count INT DEFAULT 0,
            views_count INT DEFAULT 0,
            clicks_count INT DEFAULT 0,
            engagement_rate DECIMAL(5,2) DEFAULT 0.00,
            reach_count INT DEFAULT 0,
            impressions_count INT DEFAULT 0,
            recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
            FOREIGN KEY (social_account_id) REFERENCES social_accounts(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✓ Table 'analytics' créée</p>";
    
    // Table user_sessions
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS user_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            session_token VARCHAR(255) UNIQUE NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✓ Table 'user_sessions' créée</p>";
    
    echo "<h2>5. Insertion des données de test</h2>";
    
    // Vérifier si les utilisateurs existent déjà
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    
    if ($userCount == 0) {
        // Insérer les utilisateurs de test
        $pdo->exec("
            INSERT INTO users (username, email, password_hash, first_name, last_name, plan_type) VALUES
            ('admin', '<EMAIL>', '$2y$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', 'enterprise'),
            ('demo_user', '<EMAIL>', '$2y$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Demo', 'User', 'pro')
        ");
        echo "<p style='color: green;'>✓ Utilisateurs de test créés</p>";
    } else {
        echo "<p style='color: blue;'>ℹ Utilisateurs déjà existants ($userCount utilisateurs)</p>";
    }
    
    echo "<h2>6. Création du fichier .installed</h2>";
    file_put_contents('.installed', date('Y-m-d H:i:s'));
    echo "<p style='color: green;'>✓ Fichier .installed créé</p>";
    
    echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2 style='color: #2e7d32;'>🎉 Installation terminée avec succès !</h2>";
    echo "<p><strong>Comptes de test :</strong></p>";
    echo "<ul>";
    echo "<li><strong>Admin :</strong> <EMAIL> / password</li>";
    echo "<li><strong>Demo :</strong> <EMAIL> / password</li>";
    echo "</ul>";
    echo "<p><a href='index.html' style='background: #4caf50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Lancer l'application</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #ffebee; color: #c62828; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ Erreur lors de l'installation</h3>";
    echo "<p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Suggestions :</strong></p>";
    echo "<ul>";
    echo "<li>Vérifiez que MySQL est démarré dans XAMPP</li>";
    echo "<li>Vérifiez les paramètres de connexion (host, username, password)</li>";
    echo "<li>Essayez de créer la base de données manuellement dans phpMyAdmin</li>";
    echo "</ul>";
    echo "</div>";
}
?>
