<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord - Social Scheduler</title>
    <meta name="description" content="Gérez vos publications sur les réseaux sociaux depuis votre tableau de bord Social Scheduler.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="index.html" class="logo">
                <i class="fas fa-calendar-alt"></i>
                <span>Social Scheduler</span>
            </a>
            <button class="sidebar-toggle" id="sidebar-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="dashboard.html" class="nav-link active">
                        <i class="fas fa-home"></i>
                        <span>Tableau de bord</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="calendar.html" class="nav-link">
                        <i class="fas fa-calendar"></i>
                        <span>Calendrier</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showCreatePostModal()">
                        <i class="fas fa-plus"></i>
                        <span>Nouvelle publication</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="analytics.html" class="nav-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analyses</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-share-alt"></i>
                        <span>Comptes sociaux</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>Équipe</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-images"></i>
                        <span>Bibliothèque</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="settings.html" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Paramètres</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="sidebar-footer">
            <div class="user-info" id="user-info">
                <div class="user-avatar">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="Avatar" id="user-avatar">
                </div>
                <div class="user-details">
                    <div class="user-name" id="user-name">Chargement...</div>
                    <div class="user-plan" id="user-plan">Plan Free</div>
                </div>
                <div class="user-menu">
                    <button class="user-menu-btn" onclick="toggleUserMenu()">
                        <i class="fas fa-chevron-up"></i>
                    </button>
                    <div class="user-menu-dropdown" id="user-menu-dropdown">
                        <a href="settings.html" class="menu-item">
                            <i class="fas fa-user"></i>
                            Profil
                        </a>
                        <a href="#" class="menu-item">
                            <i class="fas fa-crown"></i>
                            Upgrade
                        </a>
                        <a href="#" class="menu-item" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            Déconnexion
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-btn" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title">Tableau de bord</h1>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" onclick="showCreatePostModal()">
                    <i class="fas fa-plus"></i>
                    Nouvelle publication
                </button>
                <div class="notifications">
                    <button class="notification-btn" onclick="toggleNotifications()">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <div class="notification-dropdown" id="notification-dropdown">
                        <div class="notification-header">
                            <h3>Notifications</h3>
                            <button class="mark-all-read">Tout marquer comme lu</button>
                        </div>
                        <div class="notification-list">
                            <div class="notification-item unread">
                                <div class="notification-icon">
                                    <i class="fas fa-check-circle text-success"></i>
                                </div>
                                <div class="notification-content">
                                    <p>Publication publiée avec succès sur Facebook</p>
                                    <span class="notification-time">Il y a 5 minutes</span>
                                </div>
                            </div>
                            <div class="notification-item unread">
                                <div class="notification-icon">
                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                </div>
                                <div class="notification-content">
                                    <p>Token Instagram expiré - Reconnexion requise</p>
                                    <span class="notification-time">Il y a 1 heure</span>
                                </div>
                            </div>
                            <div class="notification-item">
                                <div class="notification-icon">
                                    <i class="fas fa-chart-line text-info"></i>
                                </div>
                                <div class="notification-content">
                                    <p>Rapport hebdomadaire disponible</p>
                                    <span class="notification-time">Il y a 2 heures</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="scheduled-posts">0</div>
                        <div class="stat-label">Publications planifiées</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +12% cette semaine
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-share-alt"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="published-posts">0</div>
                        <div class="stat-label">Publications publiées</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +8% ce mois
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="total-engagement">0</div>
                        <div class="stat-label">Engagement total</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +15% ce mois
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="total-reach">0</div>
                        <div class="stat-label">Portée totale</div>
                        <div class="stat-change negative">
                            <i class="fas fa-arrow-down"></i>
                            -3% ce mois
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="charts-section">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>Engagement des 30 derniers jours</h3>
                        <div class="chart-filters">
                            <select class="chart-filter" id="engagement-period">
                                <option value="7d">7 jours</option>
                                <option value="30d" selected>30 jours</option>
                                <option value="90d">90 jours</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="engagement-chart"></canvas>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-header">
                        <h3>Répartition par plateforme</h3>
                    </div>
                    <div class="chart-container">
                        <canvas id="platform-chart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Recent Posts -->
            <div class="recent-posts-section">
                <div class="section-header">
                    <h3>Publications récentes</h3>
                    <a href="calendar.html" class="view-all-link">Voir tout</a>
                </div>
                <div class="posts-grid" id="recent-posts">
                    <!-- Posts will be loaded here -->
                    <div class="loading-skeleton">
                        <div class="skeleton-post"></div>
                        <div class="skeleton-post"></div>
                        <div class="skeleton-post"></div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions-section">
                <div class="section-header">
                    <h3>Actions rapides</h3>
                </div>
                <div class="quick-actions-grid">
                    <button class="quick-action-card" onclick="showCreatePostModal()">
                        <div class="action-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="action-content">
                            <h4>Nouvelle publication</h4>
                            <p>Créer et planifier une publication</p>
                        </div>
                    </button>

                    <button class="quick-action-card" onclick="showAIAssistant()">
                        <div class="action-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="action-content">
                            <h4>Assistant IA</h4>
                            <p>Générer du contenu avec l'IA</p>
                        </div>
                    </button>

                    <button class="quick-action-card" onclick="showSocialAccounts()">
                        <div class="action-icon">
                            <i class="fas fa-link"></i>
                        </div>
                        <div class="action-content">
                            <h4>Connecter un compte</h4>
                            <p>Ajouter un nouveau réseau social</p>
                        </div>
                    </button>

                    <button class="quick-action-card" onclick="window.location.href='analytics.html'">
                        <div class="action-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="action-content">
                            <h4>Voir les analyses</h4>
                            <p>Consulter les performances</p>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Connected Accounts -->
            <div class="connected-accounts-section">
                <div class="section-header">
                    <h3>Comptes connectés</h3>
                    <button class="btn btn-secondary btn-sm" onclick="showSocialAccounts()">
                        <i class="fas fa-plus"></i>
                        Ajouter
                    </button>
                </div>
                <div class="accounts-grid" id="connected-accounts">
                    <!-- Accounts will be loaded here -->
                    <div class="loading-skeleton">
                        <div class="skeleton-account"></div>
                        <div class="skeleton-account"></div>
                        <div class="skeleton-account"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modals will be added here -->
    <div id="modal-container"></div>

    <!-- Alert Messages -->
    <div id="alert-container"></div>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/charts.js"></script>
</body>
</html>
