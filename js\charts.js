/**
 * Gestion des graphiques avec Chart.js
 */

class ChartsManager {
    constructor() {
        this.charts = {};
        this.colors = {
            primary: '#3b82f6',
            secondary: '#6366f1',
            success: '#10b981',
            warning: '#f59e0b',
            error: '#ef4444',
            info: '#06b6d4',
            gray: '#6b7280'
        };
    }

    init(statsData) {
        this.statsData = statsData;
        this.initEngagementChart();
        this.initPlatformChart();
    }

    initEngagementChart() {
        const ctx = document.getElementById('engagement-chart');
        if (!ctx) return;

        // Données simulées pour la démo
        const labels = this.generateDateLabels(30);
        const data = this.generateEngagementData(30);

        this.charts.engagement = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Likes',
                        data: data.likes,
                        borderColor: this.colors.primary,
                        backgroundColor: this.colors.primary + '20',
                        fill: false,
                        tension: 0.4
                    },
                    {
                        label: 'Commentaires',
                        data: data.comments,
                        borderColor: this.colors.success,
                        backgroundColor: this.colors.success + '20',
                        fill: false,
                        tension: 0.4
                    },
                    {
                        label: 'Partages',
                        data: data.shares,
                        borderColor: this.colors.warning,
                        backgroundColor: this.colors.warning + '20',
                        fill: false,
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#e5e7eb',
                        borderWidth: 1
                    }
                },
                scales: {
                    x: {
                        display: true,
                        grid: {
                            display: false
                        },
                        ticks: {
                            maxTicksLimit: 7
                        }
                    },
                    y: {
                        display: true,
                        grid: {
                            color: '#f3f4f6'
                        },
                        beginAtZero: true
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
    }

    initPlatformChart() {
        const ctx = document.getElementById('platform-chart');
        if (!ctx) return;

        // Données simulées pour la démo
        const platformData = this.generatePlatformData();

        this.charts.platform = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: platformData.labels,
                datasets: [{
                    data: platformData.data,
                    backgroundColor: [
                        this.colors.primary,
                        this.colors.success,
                        this.colors.warning,
                        this.colors.error,
                        this.colors.info,
                        this.colors.secondary
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            generateLabels: (chart) => {
                                const data = chart.data;
                                if (data.labels.length && data.datasets.length) {
                                    return data.labels.map((label, i) => {
                                        const dataset = data.datasets[0];
                                        const value = dataset.data[i];
                                        const total = dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((value / total) * 100).toFixed(1);
                                        
                                        return {
                                            text: `${label} (${percentage}%)`,
                                            fillStyle: dataset.backgroundColor[i],
                                            strokeStyle: dataset.borderColor,
                                            lineWidth: dataset.borderWidth,
                                            pointStyle: 'circle',
                                            hidden: false,
                                            index: i
                                        };
                                    });
                                }
                                return [];
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        callbacks: {
                            label: (context) => {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${value} publications (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '60%'
            }
        });
    }

    generateDateLabels(days) {
        const labels = [];
        const today = new Date();
        
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            labels.push(date.toLocaleDateString('fr-FR', { 
                month: 'short', 
                day: 'numeric' 
            }));
        }
        
        return labels;
    }

    generateEngagementData(days) {
        const likes = [];
        const comments = [];
        const shares = [];
        
        for (let i = 0; i < days; i++) {
            // Génération de données aléatoires avec une tendance
            const baseLikes = Math.floor(Math.random() * 100) + 50;
            const baseComments = Math.floor(baseLikes * 0.1) + Math.floor(Math.random() * 10);
            const baseShares = Math.floor(baseLikes * 0.05) + Math.floor(Math.random() * 5);
            
            likes.push(baseLikes);
            comments.push(baseComments);
            shares.push(baseShares);
        }
        
        return { likes, comments, shares };
    }

    generatePlatformData() {
        // Données simulées pour les plateformes
        const platforms = [
            { name: 'Facebook', posts: 45 },
            { name: 'Instagram', posts: 38 },
            { name: 'Twitter', posts: 32 },
            { name: 'LinkedIn', posts: 25 },
            { name: 'TikTok', posts: 18 },
            { name: 'YouTube', posts: 12 }
        ];
        
        return {
            labels: platforms.map(p => p.name),
            data: platforms.map(p => p.posts)
        };
    }

    updateEngagementChart(period) {
        if (!this.charts.engagement) return;
        
        const days = this.getPeriodDays(period);
        const labels = this.generateDateLabels(days);
        const data = this.generateEngagementData(days);
        
        this.charts.engagement.data.labels = labels;
        this.charts.engagement.data.datasets[0].data = data.likes;
        this.charts.engagement.data.datasets[1].data = data.comments;
        this.charts.engagement.data.datasets[2].data = data.shares;
        
        this.charts.engagement.update('active');
    }

    getPeriodDays(period) {
        switch (period) {
            case '7d': return 7;
            case '30d': return 30;
            case '90d': return 90;
            default: return 30;
        }
    }

    destroy() {
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.destroy();
            }
        });
        this.charts = {};
    }

    // Méthodes utilitaires pour les couleurs
    hexToRgba(hex, alpha = 1) {
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    getGradient(ctx, color1, color2) {
        const gradient = ctx.createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, color1);
        gradient.addColorStop(1, color2);
        return gradient;
    }
}

// Initialisation globale
document.addEventListener('DOMContentLoaded', () => {
    // Vérifier si Chart.js est chargé
    if (typeof Chart === 'undefined') {
        console.warn('Chart.js not loaded');
        return;
    }

    // Configuration globale de Chart.js
    Chart.defaults.font.family = "'Inter', sans-serif";
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#6b7280';
    Chart.defaults.borderColor = '#e5e7eb';
    Chart.defaults.backgroundColor = '#f9fafb';

    // Créer l'instance globale
    window.ChartsManager = new ChartsManager();
});

// Gestionnaire d'événements pour les filtres de graphiques
document.addEventListener('change', (e) => {
    if (e.target.id === 'engagement-period') {
        const period = e.target.value;
        if (window.ChartsManager) {
            window.ChartsManager.updateEngagementChart(period);
        }
    }
});

// Nettoyage lors du changement de page
window.addEventListener('beforeunload', () => {
    if (window.ChartsManager) {
        window.ChartsManager.destroy();
    }
});

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChartsManager;
}
