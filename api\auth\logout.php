<?php
require_once '../config/database.php';
require_once '../config/auth.php';

/**
 * API de déconnexion des utilisateurs
 */

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
    exit();
}

try {
    $auth = new Auth();
    $db = new DatabaseHelper();
    
    // Récupération de l'utilisateur actuel
    $user = $auth->getCurrentUser();
    
    if (!$user) {
        throw new Exception('Utilisateur non authentifié');
    }
    
    // Récupération du token de session depuis les en-têtes
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
    
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
        
        // Suppression de la session
        $auth->deleteSession($token);
    }
    
    // Récupération des données de la requête pour déconnexion de tous les appareils
    $input = json_decode(file_get_contents('php://input'), true);
    $logoutAll = $input['logout_all'] ?? false;
    
    if ($logoutAll) {
        // Suppression de toutes les sessions de l'utilisateur
        $db->delete('user_sessions', 'user_id = ?', [$user['id']]);
        $message = 'Déconnexion de tous les appareils réussie';
    } else {
        $message = 'Déconnexion réussie';
    }
    
    // Log de déconnexion
    error_log("Déconnexion pour : {$user['email']}");
    
    // Réponse de succès
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'message' => $message
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
