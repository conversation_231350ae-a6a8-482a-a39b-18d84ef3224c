<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Social Scheduler</title>
    <meta name="description" content="Connectez-vous à votre compte Social Scheduler pour gérer vos réseaux sociaux.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="auth-page">
    <!-- Header -->
    <header class="auth-header">
        <div class="container">
            <a href="index.html" class="logo">
                <i class="fas fa-calendar-alt"></i>
                <span>Social Scheduler</span>
            </a>
        </div>
    </header>

    <!-- Main Content -->
    <main class="auth-main">
        <div class="auth-container">
            <!-- Login Form -->
            <div class="auth-form-container" id="login-form">
                <div class="auth-form">
                    <div class="auth-header">
                        <h1 class="auth-title">Bon retour !</h1>
                        <p class="auth-subtitle">Connectez-vous à votre compte</p>
                    </div>

                    <form id="loginForm" class="form">
                        <div class="form-group">
                            <label for="login-email" class="form-label">Email ou nom d'utilisateur</label>
                            <div class="form-input-group">
                                <i class="fas fa-user form-icon"></i>
                                <input 
                                    type="text" 
                                    id="login-email" 
                                    name="login" 
                                    class="form-input" 
                                    placeholder="<EMAIL>"
                                    required
                                >
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="login-password" class="form-label">Mot de passe</label>
                            <div class="form-input-group">
                                <i class="fas fa-lock form-icon"></i>
                                <input 
                                    type="password" 
                                    id="login-password" 
                                    name="password" 
                                    class="form-input" 
                                    placeholder="••••••••"
                                    required
                                >
                                <button type="button" class="password-toggle" onclick="togglePassword('login-password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="form-options">
                            <label class="checkbox-label">
                                <input type="checkbox" name="remember_me" class="checkbox">
                                <span class="checkmark"></span>
                                Se souvenir de moi
                            </label>
                            <a href="#" class="forgot-password">Mot de passe oublié ?</a>
                        </div>

                        <button type="submit" class="btn btn-primary btn-full" id="login-btn">
                            <span class="btn-text">Se connecter</span>
                            <div class="btn-loader" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </button>

                        <div class="auth-divider">
                            <span>ou</span>
                        </div>

                        <div class="social-login">
                            <button type="button" class="btn btn-social btn-google">
                                <i class="fab fa-google"></i>
                                Continuer avec Google
                            </button>
                            <button type="button" class="btn btn-social btn-facebook">
                                <i class="fab fa-facebook-f"></i>
                                Continuer avec Facebook
                            </button>
                        </div>

                        <div class="auth-switch">
                            <p>Pas encore de compte ? 
                                <a href="#" onclick="switchToRegister()" class="auth-link">Créer un compte</a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Register Form -->
            <div class="auth-form-container" id="register-form" style="display: none;">
                <div class="auth-form">
                    <div class="auth-header">
                        <h1 class="auth-title">Créer votre compte</h1>
                        <p class="auth-subtitle">Commencez gratuitement dès aujourd'hui</p>
                    </div>

                    <form id="registerForm" class="form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="register-firstname" class="form-label">Prénom</label>
                                <div class="form-input-group">
                                    <i class="fas fa-user form-icon"></i>
                                    <input 
                                        type="text" 
                                        id="register-firstname" 
                                        name="first_name" 
                                        class="form-input" 
                                        placeholder="Jean"
                                        required
                                    >
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="register-lastname" class="form-label">Nom</label>
                                <div class="form-input-group">
                                    <i class="fas fa-user form-icon"></i>
                                    <input 
                                        type="text" 
                                        id="register-lastname" 
                                        name="last_name" 
                                        class="form-input" 
                                        placeholder="Dupont"
                                        required
                                    >
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="register-username" class="form-label">Nom d'utilisateur</label>
                            <div class="form-input-group">
                                <i class="fas fa-at form-icon"></i>
                                <input 
                                    type="text" 
                                    id="register-username" 
                                    name="username" 
                                    class="form-input" 
                                    placeholder="jeandupont"
                                    required
                                >
                            </div>
                            <div class="form-help">
                                <small>3-50 caractères, lettres, chiffres et _ uniquement</small>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="register-email" class="form-label">Email</label>
                            <div class="form-input-group">
                                <i class="fas fa-envelope form-icon"></i>
                                <input 
                                    type="email" 
                                    id="register-email" 
                                    name="email" 
                                    class="form-input" 
                                    placeholder="<EMAIL>"
                                    required
                                >
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="register-password" class="form-label">Mot de passe</label>
                            <div class="form-input-group">
                                <i class="fas fa-lock form-icon"></i>
                                <input 
                                    type="password" 
                                    id="register-password" 
                                    name="password" 
                                    class="form-input" 
                                    placeholder="••••••••"
                                    required
                                >
                                <button type="button" class="password-toggle" onclick="togglePassword('register-password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength" id="password-strength">
                                <div class="strength-bar">
                                    <div class="strength-fill"></div>
                                </div>
                                <div class="strength-text">Saisissez un mot de passe</div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="register-timezone" class="form-label">Fuseau horaire</label>
                            <div class="form-input-group">
                                <i class="fas fa-clock form-icon"></i>
                                <select id="register-timezone" name="timezone" class="form-select">
                                    <option value="Europe/Paris">Europe/Paris (UTC+1)</option>
                                    <option value="Europe/London">Europe/London (UTC+0)</option>
                                    <option value="America/New_York">America/New_York (UTC-5)</option>
                                    <option value="America/Los_Angeles">America/Los_Angeles (UTC-8)</option>
                                    <option value="Asia/Tokyo">Asia/Tokyo (UTC+9)</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="terms" class="checkbox" required>
                                <span class="checkmark"></span>
                                J'accepte les <a href="#" class="terms-link">conditions d'utilisation</a> 
                                et la <a href="#" class="terms-link">politique de confidentialité</a>
                            </label>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="newsletter" class="checkbox">
                                <span class="checkmark"></span>
                                Recevoir les actualités et conseils par email
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary btn-full" id="register-btn">
                            <span class="btn-text">Créer mon compte</span>
                            <div class="btn-loader" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </button>

                        <div class="auth-divider">
                            <span>ou</span>
                        </div>

                        <div class="social-login">
                            <button type="button" class="btn btn-social btn-google">
                                <i class="fab fa-google"></i>
                                S'inscrire avec Google
                            </button>
                            <button type="button" class="btn btn-social btn-facebook">
                                <i class="fab fa-facebook-f"></i>
                                S'inscrire avec Facebook
                            </button>
                        </div>

                        <div class="auth-switch">
                            <p>Déjà un compte ? 
                                <a href="#" onclick="switchToLogin()" class="auth-link">Se connecter</a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Features Sidebar -->
            <div class="auth-sidebar">
                <div class="sidebar-content">
                    <h2 class="sidebar-title">Pourquoi choisir Social Scheduler ?</h2>
                    
                    <div class="feature-list">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="feature-content">
                                <h3>Planification intelligente</h3>
                                <p>Programmez vos publications aux meilleurs moments pour maximiser l'engagement</p>
                            </div>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="feature-content">
                                <h3>IA intégrée</h3>
                                <p>Générez du contenu engageant avec notre assistant IA avancé</p>
                            </div>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="feature-content">
                                <h3>Analyses détaillées</h3>
                                <p>Suivez vos performances et optimisez votre stratégie social media</p>
                            </div>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="feature-content">
                                <h3>Collaboration d'équipe</h3>
                                <p>Travaillez en équipe avec des rôles et permissions personnalisés</p>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial">
                        <div class="testimonial-content">
                            <p>"Social Scheduler a révolutionné notre gestion des réseaux sociaux. Un gain de temps énorme !"</p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face" alt="Marie Dubois">
                            </div>
                            <div class="author-info">
                                <div class="author-name">Marie Dubois</div>
                                <div class="author-title">Responsable Marketing</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Alert Messages -->
    <div id="alert-container"></div>

    <!-- JavaScript -->
    <script src="js/auth.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
