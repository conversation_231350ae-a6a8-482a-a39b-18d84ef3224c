/**
 * Gestion des analyses et statistiques
 */

class AnalyticsManager {
    constructor() {
        this.analytics = null;
        this.charts = {};
        this.filters = {
            period: '30d',
            platform: ''
        };
        this.currentTab = 'top-posts';
        this.init();
    }

    async init() {
        // Vérifier l'authentification
        if (!Utils.requireAuth()) {
            return;
        }

        try {
            // Charger les données utilisateur
            await this.loadUserData();
            
            // Charger les données d'analyse
            await this.loadAnalytics();
            
            // Initialiser l'interface
            this.initUI();
            
            // Initialiser les graphiques
            this.initCharts();
            
        } catch (error) {
            console.error('Analytics initialization error:', error);
            Utils.showAlert('Erreur lors du chargement des analyses', 'error');
        }
    }

    async loadUserData() {
        const userData = localStorage.getItem('user_data');
        if (userData) {
            this.user = JSON.parse(userData);
            this.updateUserInfo();
        }
    }

    updateUserInfo() {
        if (!this.user) return;

        const userNameEl = document.getElementById('user-name');
        const userPlanEl = document.getElementById('user-plan');
        const userAvatarEl = document.getElementById('user-avatar');

        if (userNameEl) {
            userNameEl.textContent = `${this.user.first_name} ${this.user.last_name}`;
        }

        if (userPlanEl) {
            userPlanEl.textContent = `Plan ${this.user.plan_type.charAt(0).toUpperCase() + this.user.plan_type.slice(1)}`;
        }

        if (userAvatarEl && this.user.avatar_url) {
            userAvatarEl.src = this.user.avatar_url;
        }
    }

    async loadAnalytics() {
        try {
            const params = new URLSearchParams({
                metric: 'overview',
                period: this.filters.period,
                platform: this.filters.platform
            });

            const response = await Utils.apiRequest(`analytics/stats.php?${params}`);
            if (response.success) {
                this.analytics = response.data;
                this.updateOverviewStats();
                this.updateCharts();
                this.loadTopPosts();
            }
        } catch (error) {
            console.error('Analytics loading error:', error);
            // Utiliser des données simulées en cas d'erreur
            this.analytics = this.generateSampleAnalytics();
            this.updateOverviewStats();
            this.updateCharts();
            this.loadTopPosts();
        }
    }

    generateSampleAnalytics() {
        return {
            summary: {
                total_views: 12450,
                total_likes: 1890,
                total_comments: 234,
                total_shares: 156,
                total_reach: 8920,
                avg_engagement_rate: 4.2,
                period_change: {
                    views: 12.5,
                    likes: 8.3,
                    comments: 15.7,
                    shares: -2.1,
                    reach: 6.9,
                    engagement_rate: 3.2
                }
            },
            timeline: this.generateTimelineData(),
            platforms: {
                facebook: { posts: 45, engagement: 1250 },
                instagram: { posts: 38, engagement: 980 },
                twitter: { posts: 32, engagement: 650 },
                linkedin: { posts: 25, engagement: 420 }
            },
            top_posts: this.generateTopPosts()
        };
    }

    generateTimelineData() {
        const data = [];
        const days = this.getPeriodDays(this.filters.period);
        const today = new Date();

        for (let i = days - 1; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            
            data.push({
                date: date.toISOString().split('T')[0],
                likes: Math.floor(Math.random() * 100) + 20,
                comments: Math.floor(Math.random() * 30) + 5,
                shares: Math.floor(Math.random() * 20) + 2,
                views: Math.floor(Math.random() * 500) + 100
            });
        }

        return data;
    }

    generateTopPosts() {
        const posts = [];
        const platforms = ['facebook', 'instagram', 'twitter', 'linkedin'];
        
        for (let i = 1; i <= 10; i++) {
            const platform = platforms[Math.floor(Math.random() * platforms.length)];
            posts.push({
                id: i,
                title: `Publication ${i}`,
                content: `Contenu de la publication ${i} avec du texte intéressant...`,
                platform: platform,
                published_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
                metrics: {
                    views: Math.floor(Math.random() * 2000) + 500,
                    likes: Math.floor(Math.random() * 200) + 50,
                    comments: Math.floor(Math.random() * 50) + 5,
                    shares: Math.floor(Math.random() * 30) + 2,
                    engagement_rate: (Math.random() * 8 + 2).toFixed(1)
                }
            });
        }

        return posts.sort((a, b) => b.metrics.engagement_rate - a.metrics.engagement_rate);
    }

    getPeriodDays(period) {
        switch (period) {
            case '7d': return 7;
            case '30d': return 30;
            case '90d': return 90;
            case '1y': return 365;
            default: return 30;
        }
    }

    updateOverviewStats() {
        if (!this.analytics) return;

        const summary = this.analytics.summary;
        
        // Mettre à jour les valeurs
        this.updateStatValue('total-views', summary.total_views);
        this.updateStatValue('total-likes', summary.total_likes);
        this.updateStatValue('total-comments', summary.total_comments);
        this.updateStatValue('total-shares', summary.total_shares);
        this.updateStatValue('total-reach', summary.total_reach);
        this.updateStatValue('engagement-rate', summary.avg_engagement_rate, '%');

        // Mettre à jour les changements
        if (summary.period_change) {
            this.updateStatChange('views-change', summary.period_change.views);
            this.updateStatChange('likes-change', summary.period_change.likes);
            this.updateStatChange('comments-change', summary.period_change.comments);
            this.updateStatChange('shares-change', summary.period_change.shares);
            this.updateStatChange('reach-change', summary.period_change.reach);
            this.updateStatChange('engagement-change', summary.period_change.engagement_rate);
        }
    }

    updateStatValue(elementId, value, suffix = '') {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = Utils.formatNumber(value) + suffix;
        }
    }

    updateStatChange(elementId, change) {
        const element = document.getElementById(elementId);
        if (element) {
            const isPositive = change >= 0;
            const icon = isPositive ? 'fa-arrow-up' : 'fa-arrow-down';
            const sign = isPositive ? '+' : '';
            
            element.className = `stat-change ${isPositive ? 'positive' : 'negative'}`;
            element.innerHTML = `
                <i class="fas ${icon}"></i>
                ${sign}${change.toFixed(1)}%
            `;
        }
    }

    initCharts() {
        this.initEngagementChart();
        this.initPlatformChart();
    }

    initEngagementChart() {
        const ctx = document.getElementById('engagement-chart');
        if (!ctx || !this.analytics) return;

        const timeline = this.analytics.timeline;
        const labels = timeline.map(item => {
            const date = new Date(item.date);
            return date.toLocaleDateString('fr-FR', { month: 'short', day: 'numeric' });
        });

        this.charts.engagement = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'J\'aime',
                        data: timeline.map(item => item.likes),
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: false,
                        tension: 0.4
                    },
                    {
                        label: 'Commentaires',
                        data: timeline.map(item => item.comments),
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        fill: false,
                        tension: 0.4
                    },
                    {
                        label: 'Partages',
                        data: timeline.map(item => item.shares),
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        fill: false,
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    initPlatformChart() {
        const ctx = document.getElementById('platform-chart');
        if (!ctx || !this.analytics) return;

        const platforms = this.analytics.platforms;
        const labels = Object.keys(platforms).map(key => 
            key.charAt(0).toUpperCase() + key.slice(1)
        );
        const data = Object.values(platforms).map(platform => platform.engagement);

        this.charts.platform = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: [
                        '#3b82f6',
                        '#10b981',
                        '#f59e0b',
                        '#ef4444',
                        '#8b5cf6',
                        '#06b6d4'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    updateCharts() {
        if (this.charts.engagement) {
            this.updateEngagementChart();
        }
        if (this.charts.platform) {
            this.updatePlatformChart();
        }
    }

    updateEngagementChart() {
        const timeline = this.analytics.timeline;
        const labels = timeline.map(item => {
            const date = new Date(item.date);
            return date.toLocaleDateString('fr-FR', { month: 'short', day: 'numeric' });
        });

        this.charts.engagement.data.labels = labels;
        this.charts.engagement.data.datasets[0].data = timeline.map(item => item.likes);
        this.charts.engagement.data.datasets[1].data = timeline.map(item => item.comments);
        this.charts.engagement.data.datasets[2].data = timeline.map(item => item.shares);
        
        this.charts.engagement.update();
    }

    updatePlatformChart() {
        const platforms = this.analytics.platforms;
        const data = Object.values(platforms).map(platform => platform.engagement);
        
        this.charts.platform.data.datasets[0].data = data;
        this.charts.platform.update();
    }

    async loadTopPosts() {
        const container = document.getElementById('top-posts-list');
        if (!container) return;

        const posts = this.analytics.top_posts || [];
        
        if (posts.length === 0) {
            container.innerHTML = '<p class="no-data">Aucune donnée disponible</p>';
            return;
        }

        const postsHTML = posts.slice(0, 5).map((post, index) => this.createPostPerformanceCard(post, index + 1)).join('');
        container.innerHTML = postsHTML;
    }

    createPostPerformanceCard(post, rank) {
        const platformIcon = this.getPlatformIcon(post.platform);
        
        return `
            <div class="post-performance-card">
                <div class="post-rank">#${rank}</div>
                <div class="post-info">
                    <div class="post-header">
                        <h4 class="post-title">${Utils.escapeHtml(post.title || 'Sans titre')}</h4>
                        <div class="post-platform">
                            <i class="${platformIcon}"></i>
                            ${post.platform.charAt(0).toUpperCase() + post.platform.slice(1)}
                        </div>
                    </div>
                    <p class="post-content">${Utils.truncateText(Utils.escapeHtml(post.content), 100)}</p>
                    <div class="post-date">
                        <i class="fas fa-clock"></i>
                        ${Utils.formatDate(post.published_at)}
                    </div>
                </div>
                <div class="post-metrics">
                    <div class="metric">
                        <div class="metric-value">${Utils.formatNumber(post.metrics.views)}</div>
                        <div class="metric-label">Vues</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">${Utils.formatNumber(post.metrics.likes)}</div>
                        <div class="metric-label">J'aime</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">${Utils.formatNumber(post.metrics.comments)}</div>
                        <div class="metric-label">Commentaires</div>
                    </div>
                    <div class="metric highlight">
                        <div class="metric-value">${post.metrics.engagement_rate}%</div>
                        <div class="metric-label">Engagement</div>
                    </div>
                </div>
            </div>
        `;
    }

    getPlatformIcon(platform) {
        const icons = {
            facebook: 'fab fa-facebook',
            instagram: 'fab fa-instagram',
            twitter: 'fab fa-twitter',
            linkedin: 'fab fa-linkedin',
            tiktok: 'fab fa-tiktok',
            youtube: 'fab fa-youtube'
        };
        return icons[platform] || 'fas fa-share-alt';
    }

    initUI() {
        // Gestionnaires pour les filtres
        const periodFilter = document.getElementById('period-filter');
        const platformFilter = document.getElementById('platform-filter');
        
        if (periodFilter) {
            periodFilter.addEventListener('change', (e) => {
                this.filters.period = e.target.value;
                this.loadAnalytics();
            });
        }
        
        if (platformFilter) {
            platformFilter.addEventListener('change', (e) => {
                this.filters.platform = e.target.value;
                this.loadAnalytics();
            });
        }

        // Gestionnaires pour les onglets
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.target.dataset.tab;
                this.switchTab(tab);
            });
        });

        // Gestionnaire pour le filtre de métrique d'engagement
        const engagementMetric = document.getElementById('engagement-metric');
        if (engagementMetric) {
            engagementMetric.addEventListener('change', (e) => {
                this.updateEngagementChartMetric(e.target.value);
            });
        }
    }

    switchTab(tabName) {
        // Mettre à jour les boutons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Mettre à jour le contenu
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;
        this.loadTabContent(tabName);
    }

    async loadTabContent(tabName) {
        const container = document.getElementById(`${tabName}-list`);
        if (!container) return;

        let posts = [];
        
        switch (tabName) {
            case 'top-posts':
                posts = this.analytics.top_posts || [];
                break;
            case 'recent-posts':
                posts = (this.analytics.top_posts || []).slice().reverse();
                break;
            case 'low-performing':
                posts = (this.analytics.top_posts || []).slice().sort((a, b) => 
                    a.metrics.engagement_rate - b.metrics.engagement_rate
                );
                break;
        }

        if (posts.length === 0) {
            container.innerHTML = '<p class="no-data">Aucune donnée disponible</p>';
            return;
        }

        const postsHTML = posts.slice(0, 5).map((post, index) => 
            this.createPostPerformanceCard(post, index + 1)
        ).join('');
        container.innerHTML = postsHTML;
    }

    updateEngagementChartMetric(metric) {
        if (!this.charts.engagement || !this.analytics) return;

        const timeline = this.analytics.timeline;
        
        if (metric === 'all') {
            // Afficher toutes les métriques
            this.charts.engagement.data.datasets.forEach(dataset => {
                dataset.hidden = false;
            });
        } else {
            // Afficher seulement la métrique sélectionnée
            this.charts.engagement.data.datasets.forEach((dataset, index) => {
                const metrics = ['likes', 'comments', 'shares'];
                dataset.hidden = metrics[index] !== metric;
            });
        }
        
        this.charts.engagement.update();
    }
}

// Fonctions globales
function exportReport() {
    Utils.showAlert('Export du rapport en cours de développement', 'info');
}

function showCreatePostModal() {
    Utils.showAlert('Redirection vers la création de publication...', 'info');
    setTimeout(() => {
        window.location.href = 'posts.html';
    }, 1000);
}

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    window.analyticsManager = new AnalyticsManager();
});
