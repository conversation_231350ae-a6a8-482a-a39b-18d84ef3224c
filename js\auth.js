/**
 * Gestion de l'authentification
 */

class AuthManager {
    constructor() {
        this.init();
    }

    init() {
        // Vérifier le mode depuis l'URL
        const urlParams = new URLSearchParams(window.location.search);
        const mode = urlParams.get('mode');
        
        if (mode === 'register') {
            this.switchToRegister();
        }

        // Initialiser les formulaires
        this.initForms();
        this.initPasswordStrength();
    }

    initForms() {
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');

        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        }
    }

    initPasswordStrength() {
        const passwordInput = document.getElementById('register-password');
        const strengthIndicator = document.getElementById('password-strength');

        if (passwordInput && strengthIndicator) {
            passwordInput.addEventListener('input', (e) => {
                this.updatePasswordStrength(e.target.value, strengthIndicator);
            });
        }
    }

    updatePasswordStrength(password, indicator) {
        const strength = this.calculatePasswordStrength(password);
        const strengthBar = indicator.querySelector('.strength-fill');
        const strengthText = indicator.querySelector('.strength-text');

        // Mise à jour de la barre
        strengthBar.style.width = `${strength.percentage}%`;
        strengthBar.className = `strength-fill strength-${strength.level}`;

        // Mise à jour du texte
        strengthText.textContent = strength.text;
        strengthText.className = `strength-text text-${strength.level}`;
    }

    calculatePasswordStrength(password) {
        let score = 0;
        let feedback = [];

        if (password.length === 0) {
            return { level: 'empty', percentage: 0, text: 'Saisissez un mot de passe' };
        }

        // Longueur
        if (password.length >= 8) score += 25;
        else feedback.push('au moins 8 caractères');

        // Minuscules
        if (/[a-z]/.test(password)) score += 25;
        else feedback.push('une minuscule');

        // Majuscules
        if (/[A-Z]/.test(password)) score += 25;
        else feedback.push('une majuscule');

        // Chiffres
        if (/\d/.test(password)) score += 25;
        else feedback.push('un chiffre');

        // Caractères spéciaux (bonus)
        if (/[^a-zA-Z\d]/.test(password)) score += 10;

        let level, text;
        if (score < 25) {
            level = 'weak';
            text = 'Faible';
        } else if (score < 50) {
            level = 'fair';
            text = 'Moyen';
        } else if (score < 75) {
            level = 'good';
            text = 'Bon';
        } else {
            level = 'strong';
            text = 'Fort';
        }

        if (feedback.length > 0 && score < 100) {
            text += ` - Ajoutez: ${feedback.join(', ')}`;
        }

        return { level, percentage: Math.min(score, 100), text };
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        // Désactiver le bouton et afficher le loader
        this.setButtonLoading(submitBtn, true);
        
        try {
            const response = await Utils.apiRequest('auth/login.php', {
                method: 'POST',
                body: JSON.stringify({
                    login: formData.get('login'),
                    password: formData.get('password'),
                    remember_me: formData.get('remember_me') === 'on'
                })
            });

            if (response.success) {
                // Sauvegarder les données d'authentification
                localStorage.setItem('auth_token', response.data.token);
                localStorage.setItem('user_data', JSON.stringify(response.data.user));
                
                Utils.showAlert('Connexion réussie ! Redirection...', 'success');
                
                // Redirection après un court délai
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1000);
            }
        } catch (error) {
            Utils.showAlert(error.message || 'Erreur de connexion', 'error');
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }

    async handleRegister(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        // Validation côté client
        const validationError = this.validateRegistrationForm(formData);
        if (validationError) {
            Utils.showAlert(validationError, 'error');
            return;
        }
        
        // Désactiver le bouton et afficher le loader
        this.setButtonLoading(submitBtn, true);
        
        try {
            const response = await Utils.apiRequest('auth/register.php', {
                method: 'POST',
                body: JSON.stringify({
                    username: formData.get('username'),
                    email: formData.get('email'),
                    password: formData.get('password'),
                    first_name: formData.get('first_name'),
                    last_name: formData.get('last_name'),
                    timezone: formData.get('timezone'),
                    language: 'fr'
                })
            });

            if (response.success) {
                // Sauvegarder les données d'authentification
                localStorage.setItem('auth_token', response.data.token);
                localStorage.setItem('user_data', JSON.stringify(response.data.user));
                
                Utils.showAlert('Inscription réussie ! Redirection...', 'success');
                
                // Redirection après un court délai
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1000);
            }
        } catch (error) {
            Utils.showAlert(error.message || 'Erreur d\'inscription', 'error');
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }

    validateRegistrationForm(formData) {
        const email = formData.get('email');
        const password = formData.get('password');
        const username = formData.get('username');
        const firstName = formData.get('first_name');
        const lastName = formData.get('last_name');
        const terms = formData.get('terms');

        if (!firstName || firstName.trim().length < 2) {
            return 'Le prénom doit contenir au moins 2 caractères';
        }

        if (!lastName || lastName.trim().length < 2) {
            return 'Le nom doit contenir au moins 2 caractères';
        }

        if (!username || username.length < 3 || username.length > 50) {
            return 'Le nom d\'utilisateur doit contenir entre 3 et 50 caractères';
        }

        if (!/^[a-zA-Z0-9_]+$/.test(username)) {
            return 'Le nom d\'utilisateur ne peut contenir que des lettres, chiffres et underscores';
        }

        if (!Utils.validateEmail(email)) {
            return 'Format d\'email invalide';
        }

        if (!Utils.validatePassword(password)) {
            return 'Le mot de passe doit contenir au moins 8 caractères, une majuscule, une minuscule et un chiffre';
        }

        if (!terms) {
            return 'Vous devez accepter les conditions d\'utilisation';
        }

        return null;
    }

    setButtonLoading(button, loading) {
        const btnText = button.querySelector('.btn-text');
        const btnLoader = button.querySelector('.btn-loader');
        
        if (loading) {
            button.disabled = true;
            btnText.style.display = 'none';
            btnLoader.style.display = 'block';
        } else {
            button.disabled = false;
            btnText.style.display = 'block';
            btnLoader.style.display = 'none';
        }
    }

    switchToRegister() {
        const loginForm = document.getElementById('login-form');
        const registerForm = document.getElementById('register-form');
        
        if (loginForm && registerForm) {
            loginForm.style.display = 'none';
            registerForm.style.display = 'block';
            
            // Mettre à jour l'URL sans recharger la page
            const url = new URL(window.location);
            url.searchParams.set('mode', 'register');
            window.history.replaceState({}, '', url);
        }
    }

    switchToLogin() {
        const loginForm = document.getElementById('login-form');
        const registerForm = document.getElementById('register-form');
        
        if (loginForm && registerForm) {
            loginForm.style.display = 'block';
            registerForm.style.display = 'none';
            
            // Mettre à jour l'URL sans recharger la page
            const url = new URL(window.location);
            url.searchParams.delete('mode');
            window.history.replaceState({}, '', url);
        }
    }
}

// Fonctions globales pour les boutons de commutation
function switchToRegister() {
    authManager.switchToRegister();
}

function switchToLogin() {
    authManager.switchToLogin();
}

// Initialisation quand le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
    // Rediriger si déjà connecté
    if (Utils.isAuthenticated()) {
        window.location.href = 'dashboard.html';
        return;
    }
    
    // Initialiser le gestionnaire d'authentification
    window.authManager = new AuthManager();
});

// Styles CSS pour la force du mot de passe
if (!document.getElementById('password-strength-styles')) {
    const style = document.createElement('style');
    style.id = 'password-strength-styles';
    style.textContent = `
        .password-strength {
            margin-top: 8px;
        }
        
        .strength-bar {
            height: 4px;
            background-color: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 4px;
        }
        
        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }
        
        .strength-fill.strength-weak {
            background-color: #ef4444;
        }
        
        .strength-fill.strength-fair {
            background-color: #f59e0b;
        }
        
        .strength-fill.strength-good {
            background-color: #3b82f6;
        }
        
        .strength-fill.strength-strong {
            background-color: #10b981;
        }
        
        .strength-text {
            font-size: 12px;
            font-weight: 500;
        }
        
        .strength-text.text-weak {
            color: #ef4444;
        }
        
        .strength-text.text-fair {
            color: #f59e0b;
        }
        
        .strength-text.text-good {
            color: #3b82f6;
        }
        
        .strength-text.text-strong {
            color: #10b981;
        }
        
        .strength-text.text-empty {
            color: #6b7280;
        }
    `;
    document.head.appendChild(style);
}
