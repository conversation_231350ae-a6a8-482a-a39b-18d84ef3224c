# Configuration Apache pour Social Scheduler

# Activer la réécriture d'URL
RewriteEngine On

# Redirection HTTPS (optionnel, décommenter en production)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Gestion des erreurs personnalisées
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html

# Sécurité - Masquer les informations du serveur
ServerTokens Prod
ServerSignature Off

# Sécurité - Headers de sécurité
<IfModule mod_headers.c>
    # Protection XSS
    Header always set X-XSS-Protection "1; mode=block"
    
    # Prévention du MIME sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # Protection contre le clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # Politique de référent
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (à ajuster selon vos besoins)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self'"
</IfModule>

# Compression GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache des fichiers statiques
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS et JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # Fonts
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # JSON et XML
    ExpiresByType application/json "access plus 1 hour"
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType text/xml "access plus 1 hour"
</IfModule>

# Protection des fichiers sensibles
<Files "*.php">
    <RequireAll>
        Require all granted
    </RequireAll>
</Files>

# Bloquer l'accès aux fichiers de configuration
<FilesMatch "\.(env|ini|log|sh|sql)$">
    Require all denied
</FilesMatch>

# Bloquer l'accès aux dossiers système
<DirectoryMatch "^(.*/)?\.git/">
    Require all denied
</DirectoryMatch>

# Bloquer l'accès aux fichiers de sauvegarde
<FilesMatch "\.(bak|backup|old|tmp)$">
    Require all denied
</FilesMatch>

# Redirection pour les API
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/$1 [L]

# Redirection pour les pages sans extension
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^.]+)$ $1.html [NC,L]

# Redirection de la racine vers index.html
DirectoryIndex index.html index.php

# Limitation de la taille des uploads (ajuster selon vos besoins)
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300

# Désactiver l'affichage des erreurs PHP en production
# php_flag display_errors Off
# php_flag log_errors On
# php_value error_log /path/to/your/error.log

# Optimisation des performances
<IfModule mod_headers.c>
    # Cache des ressources statiques
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
    # Pas de cache pour les fichiers dynamiques
    <FilesMatch "\.(php|html)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
</IfModule>

# Protection contre les attaques par force brute
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>

# Limitation du taux de requêtes (si mod_evasive n'est pas disponible)
<IfModule mod_security2.c>
    SecRuleEngine On
    SecRule REQUEST_URI "@streq /api/" \
        "id:1001,phase:1,block,msg:'API rate limit exceeded',\
        setvar:ip.api_requests=+1,expirevar:ip.api_requests=60,\
        chain"
    SecRule IP:API_REQUESTS "@gt 100" \
        "setvar:ip.blocked=1,expirevar:ip.blocked=3600"
</IfModule>
