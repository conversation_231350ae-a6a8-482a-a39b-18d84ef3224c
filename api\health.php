<?php
/**
 * Endpoint de vérification de l'état de l'API
 */

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Headers CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=UTF-8');

// Gestion des requêtes OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // Vérifier si le fichier de configuration existe
    if (!file_exists('config/database.php')) {
        throw new Exception('Fichier de configuration manquant');
    }
    
    // Inclure la configuration
    require_once 'config/database.php';
    
    // Tester la connexion à la base de données
    $db = new Database();
    $conn = $db->getConnection();
    
    if (!$conn) {
        throw new Exception('Impossible de se connecter à la base de données');
    }
    
    // Vérifier si les tables existent
    $stmt = $conn->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_tables = ['users', 'posts', 'social_accounts', 'teams'];
    $missing_tables = array_diff($required_tables, $tables);
    
    $response = [
        'status' => 'ok',
        'message' => 'API fonctionnelle',
        'timestamp' => date('Y-m-d H:i:s'),
        'php_version' => phpversion(),
        'database' => [
            'connected' => true,
            'tables_count' => count($tables),
            'missing_tables' => $missing_tables
        ],
        'extensions' => [
            'pdo' => extension_loaded('pdo'),
            'pdo_mysql' => extension_loaded('pdo_mysql'),
            'json' => extension_loaded('json'),
            'openssl' => extension_loaded('openssl')
        ]
    ];
    
    if (!empty($missing_tables)) {
        $response['status'] = 'warning';
        $response['message'] = 'Tables manquantes détectées';
    }
    
    http_response_code(200);
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s'),
        'suggestion' => 'Exécutez install.php pour configurer l\'application'
    ], JSON_PRETTY_PRINT);
}
?>
