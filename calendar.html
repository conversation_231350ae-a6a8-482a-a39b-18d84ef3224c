<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendrier - Social Scheduler</title>
    <meta name="description" content="Visualisez et gérez vos publications planifiées avec le calendrier Social Scheduler.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- FullCalendar -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="index.html" class="logo">
                <i class="fas fa-calendar-alt"></i>
                <span>Social Scheduler</span>
            </a>
            <button class="sidebar-toggle" id="sidebar-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="dashboard.html" class="nav-link">
                        <i class="fas fa-home"></i>
                        <span>Tableau de bord</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="calendar.html" class="nav-link active">
                        <i class="fas fa-calendar"></i>
                        <span>Calendrier</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showCreatePostModal()">
                        <i class="fas fa-plus"></i>
                        <span>Nouvelle publication</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="analytics.html" class="nav-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analyses</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-share-alt"></i>
                        <span>Comptes sociaux</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>Équipe</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-images"></i>
                        <span>Bibliothèque</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="settings.html" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Paramètres</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="sidebar-footer">
            <div class="user-info" id="user-info">
                <div class="user-avatar">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="Avatar" id="user-avatar">
                </div>
                <div class="user-details">
                    <div class="user-name" id="user-name">Chargement...</div>
                    <div class="user-plan" id="user-plan">Plan Free</div>
                </div>
                <div class="user-menu">
                    <button class="user-menu-btn" onclick="toggleUserMenu()">
                        <i class="fas fa-chevron-up"></i>
                    </button>
                    <div class="user-menu-dropdown" id="user-menu-dropdown">
                        <a href="settings.html" class="menu-item">
                            <i class="fas fa-user"></i>
                            Profil
                        </a>
                        <a href="#" class="menu-item">
                            <i class="fas fa-crown"></i>
                            Upgrade
                        </a>
                        <a href="#" class="menu-item" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            Déconnexion
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-btn" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title">Calendrier</h1>
            </div>
            <div class="header-right">
                <div class="calendar-controls">
                    <div class="view-selector">
                        <button class="view-btn active" data-view="dayGridMonth">
                            <i class="fas fa-calendar"></i>
                            Mois
                        </button>
                        <button class="view-btn" data-view="timeGridWeek">
                            <i class="fas fa-calendar-week"></i>
                            Semaine
                        </button>
                        <button class="view-btn" data-view="timeGridDay">
                            <i class="fas fa-calendar-day"></i>
                            Jour
                        </button>
                    </div>
                    <div class="calendar-filters">
                        <select id="platform-filter" class="filter-select">
                            <option value="">Toutes les plateformes</option>
                            <option value="facebook">Facebook</option>
                            <option value="instagram">Instagram</option>
                            <option value="twitter">Twitter</option>
                            <option value="linkedin">LinkedIn</option>
                            <option value="tiktok">TikTok</option>
                            <option value="youtube">YouTube</option>
                        </select>
                        <select id="status-filter" class="filter-select">
                            <option value="">Tous les statuts</option>
                            <option value="scheduled">Planifié</option>
                            <option value="published">Publié</option>
                            <option value="failed">Échec</option>
                        </select>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="showCreatePostModal()">
                    <i class="fas fa-plus"></i>
                    Nouvelle publication
                </button>
            </div>
        </header>

        <!-- Calendar Content -->
        <div class="calendar-content">
            <div class="calendar-sidebar">
                <div class="sidebar-section">
                    <h3 class="sidebar-title">Légende</h3>
                    <div class="legend-list">
                        <div class="legend-item">
                            <span class="legend-color status-scheduled"></span>
                            <span class="legend-label">Planifié</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color status-published"></span>
                            <span class="legend-label">Publié</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color status-failed"></span>
                            <span class="legend-label">Échec</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color status-draft"></span>
                            <span class="legend-label">Brouillon</span>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3 class="sidebar-title">Plateformes</h3>
                    <div class="platform-list">
                        <div class="platform-item">
                            <label class="platform-checkbox">
                                <input type="checkbox" checked data-platform="facebook">
                                <span class="checkmark"></span>
                                <i class="fab fa-facebook platform-icon"></i>
                                Facebook
                            </label>
                        </div>
                        <div class="platform-item">
                            <label class="platform-checkbox">
                                <input type="checkbox" checked data-platform="instagram">
                                <span class="checkmark"></span>
                                <i class="fab fa-instagram platform-icon"></i>
                                Instagram
                            </label>
                        </div>
                        <div class="platform-item">
                            <label class="platform-checkbox">
                                <input type="checkbox" checked data-platform="twitter">
                                <span class="checkmark"></span>
                                <i class="fab fa-twitter platform-icon"></i>
                                Twitter
                            </label>
                        </div>
                        <div class="platform-item">
                            <label class="platform-checkbox">
                                <input type="checkbox" checked data-platform="linkedin">
                                <span class="checkmark"></span>
                                <i class="fab fa-linkedin platform-icon"></i>
                                LinkedIn
                            </label>
                        </div>
                        <div class="platform-item">
                            <label class="platform-checkbox">
                                <input type="checkbox" checked data-platform="tiktok">
                                <span class="checkmark"></span>
                                <i class="fab fa-tiktok platform-icon"></i>
                                TikTok
                            </label>
                        </div>
                        <div class="platform-item">
                            <label class="platform-checkbox">
                                <input type="checkbox" checked data-platform="youtube">
                                <span class="checkmark"></span>
                                <i class="fab fa-youtube platform-icon"></i>
                                YouTube
                            </label>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3 class="sidebar-title">Statistiques du mois</h3>
                    <div class="stats-list">
                        <div class="stat-item">
                            <div class="stat-value" id="month-scheduled">0</div>
                            <div class="stat-label">Planifiées</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="month-published">0</div>
                            <div class="stat-label">Publiées</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="month-failed">0</div>
                            <div class="stat-label">Échecs</div>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3 class="sidebar-title">Actions rapides</h3>
                    <div class="quick-actions">
                        <button class="quick-action-btn" onclick="showCreatePostModal()">
                            <i class="fas fa-plus"></i>
                            Nouvelle publication
                        </button>
                        <button class="quick-action-btn" onclick="showBulkScheduler()">
                            <i class="fas fa-calendar-plus"></i>
                            Planification en lot
                        </button>
                        <button class="quick-action-btn" onclick="exportCalendar()">
                            <i class="fas fa-download"></i>
                            Exporter
                        </button>
                    </div>
                </div>
            </div>

            <div class="calendar-main">
                <div id="calendar"></div>
            </div>
        </div>
    </main>

    <!-- Event Details Modal -->
    <div id="event-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Détails de la publication</h3>
                <button class="modal-close" onclick="closeEventModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="event-details">
                <!-- Event details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeEventModal()">Fermer</button>
                <button class="btn btn-primary" onclick="editEvent()">Modifier</button>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div id="alert-container"></div>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/calendar.js"></script>
</body>
</html>
