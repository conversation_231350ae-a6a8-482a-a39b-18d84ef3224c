/**
 * Gestion du tableau de bord
 */

class DashboardManager {
    constructor() {
        this.user = null;
        this.stats = null;
        this.posts = [];
        this.socialAccounts = [];
        this.init();
    }

    async init() {
        // Vérifier l'authentification
        if (!Utils.requireAuth()) {
            return;
        }

        try {
            // Charger les données utilisateur
            await this.loadUserData();
            
            // Charger les statistiques
            await this.loadStats();
            
            // Charger les publications récentes
            await this.loadRecentPosts();
            
            // Charger les comptes sociaux
            await this.loadSocialAccounts();
            
            // Initialiser l'interface
            this.initUI();
            
        } catch (error) {
            console.error('Dashboard initialization error:', error);
            Utils.showAlert('Erreur lors du chargement du tableau de bord', 'error');
        }
    }

    async loadUserData() {
        // Récupérer depuis le localStorage d'abord
        const userData = localStorage.getItem('user_data');
        if (userData) {
            this.user = JSON.parse(userData);
            this.updateUserInfo();
        }

        // Puis rafraîchir depuis l'API si nécessaire
        // Cette fonctionnalité pourrait être ajoutée plus tard
    }

    async loadStats() {
        try {
            const response = await Utils.apiRequest('analytics/stats.php?metric=overview&period=30d');
            if (response.success) {
                this.stats = response.data;
                this.updateStatsDisplay();
            }
        } catch (error) {
            console.error('Stats loading error:', error);
            // Utiliser des données par défaut en cas d'erreur
            this.stats = {
                summary: {
                    total_posts: 0,
                    total_likes: 0,
                    total_comments: 0,
                    total_shares: 0,
                    total_views: 0,
                    avg_engagement_rate: 0,
                    total_reach: 0,
                    total_impressions: 0
                }
            };
            this.updateStatsDisplay();
        }
    }

    async loadRecentPosts() {
        try {
            const response = await Utils.apiRequest('posts/list.php?limit=6&sort_by=created_at&sort_order=DESC');
            if (response.success) {
                this.posts = response.data.posts;
                this.updateRecentPostsDisplay();
            }
        } catch (error) {
            console.error('Posts loading error:', error);
            this.posts = [];
            this.updateRecentPostsDisplay();
        }
    }

    async loadSocialAccounts() {
        try {
            const response = await Utils.apiRequest('social/connect.php', {
                method: 'POST',
                body: JSON.stringify({ action: 'list' })
            });
            if (response.success) {
                this.socialAccounts = response.data.accounts;
                this.updateSocialAccountsDisplay();
            }
        } catch (error) {
            console.error('Social accounts loading error:', error);
            this.socialAccounts = [];
            this.updateSocialAccountsDisplay();
        }
    }

    updateUserInfo() {
        if (!this.user) return;

        const userNameEl = document.getElementById('user-name');
        const userPlanEl = document.getElementById('user-plan');
        const userAvatarEl = document.getElementById('user-avatar');

        if (userNameEl) {
            userNameEl.textContent = `${this.user.first_name} ${this.user.last_name}`;
        }

        if (userPlanEl) {
            userPlanEl.textContent = `Plan ${this.user.plan_type.charAt(0).toUpperCase() + this.user.plan_type.slice(1)}`;
        }

        if (userAvatarEl && this.user.avatar_url) {
            userAvatarEl.src = this.user.avatar_url;
        }
    }

    updateStatsDisplay() {
        if (!this.stats) return;

        const summary = this.stats.summary;

        // Mettre à jour les cartes de statistiques
        this.updateStatCard('scheduled-posts', summary.total_posts || 0);
        this.updateStatCard('published-posts', summary.total_posts || 0);
        this.updateStatCard('total-engagement', 
            (summary.total_likes + summary.total_comments + summary.total_shares) || 0);
        this.updateStatCard('total-reach', summary.total_reach || 0);
    }

    updateStatCard(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = Utils.formatNumber(value);
        }
    }

    updateRecentPostsDisplay() {
        const container = document.getElementById('recent-posts');
        if (!container) return;

        if (this.posts.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <h3>Aucune publication</h3>
                    <p>Créez votre première publication pour commencer</p>
                    <button class="btn btn-primary" onclick="showCreatePostModal()">
                        <i class="fas fa-plus"></i>
                        Créer une publication
                    </button>
                </div>
            `;
            return;
        }

        const postsHTML = this.posts.map(post => this.createPostCard(post)).join('');
        container.innerHTML = postsHTML;
    }

    createPostCard(post) {
        const statusClass = this.getStatusClass(post.status);
        const statusText = this.getStatusText(post.status);
        const platforms = post.scheduled_accounts?.map(acc => acc.platform).join(', ') || 'Aucune plateforme';
        
        return `
            <div class="post-card" data-post-id="${post.id}">
                <div class="post-header">
                    <div class="post-platforms">
                        ${this.createPlatformIcons(post.scheduled_accounts || [])}
                    </div>
                    <span class="post-status ${statusClass}">${statusText}</span>
                </div>
                <div class="post-content">
                    <h4 class="post-title">${Utils.escapeHtml(post.title || 'Sans titre')}</h4>
                    <p class="post-text">${Utils.truncateText(Utils.escapeHtml(post.content), 100)}</p>
                </div>
                <div class="post-footer">
                    <div class="post-meta">
                        <span class="post-date">
                            <i class="fas fa-clock"></i>
                            ${Utils.formatDate(post.scheduled_at || post.created_at)}
                        </span>
                    </div>
                    <div class="post-actions">
                        <button class="btn-icon" onclick="editPost(${post.id})" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon" onclick="duplicatePost(${post.id})" title="Dupliquer">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button class="btn-icon text-error" onclick="deletePost(${post.id})" title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    createPlatformIcons(accounts) {
        if (!accounts || accounts.length === 0) {
            return '<span class="no-platforms">Aucune plateforme</span>';
        }

        const platformIcons = {
            facebook: 'fab fa-facebook',
            instagram: 'fab fa-instagram',
            twitter: 'fab fa-twitter',
            linkedin: 'fab fa-linkedin',
            tiktok: 'fab fa-tiktok',
            youtube: 'fab fa-youtube',
            pinterest: 'fab fa-pinterest',
            reddit: 'fab fa-reddit'
        };

        return accounts.map(account => 
            `<i class="${platformIcons[account.platform] || 'fas fa-share-alt'}" title="${account.platform}"></i>`
        ).join('');
    }

    getStatusClass(status) {
        const classes = {
            draft: 'status-draft',
            scheduled: 'status-scheduled',
            published: 'status-published',
            failed: 'status-failed'
        };
        return classes[status] || 'status-draft';
    }

    getStatusText(status) {
        const texts = {
            draft: 'Brouillon',
            scheduled: 'Planifié',
            published: 'Publié',
            failed: 'Échec'
        };
        return texts[status] || 'Inconnu';
    }

    updateSocialAccountsDisplay() {
        const container = document.getElementById('connected-accounts');
        if (!container) return;

        if (this.socialAccounts.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <h3>Aucun compte connecté</h3>
                    <p>Connectez vos comptes de réseaux sociaux pour commencer</p>
                    <button class="btn btn-primary" onclick="showSocialAccounts()">
                        <i class="fas fa-plus"></i>
                        Connecter un compte
                    </button>
                </div>
            `;
            return;
        }

        const accountsHTML = this.socialAccounts.map(account => this.createAccountCard(account)).join('');
        container.innerHTML = accountsHTML;
    }

    createAccountCard(account) {
        const platformIcons = {
            facebook: 'fab fa-facebook',
            instagram: 'fab fa-instagram',
            twitter: 'fab fa-twitter',
            linkedin: 'fab fa-linkedin',
            tiktok: 'fab fa-tiktok',
            youtube: 'fab fa-youtube',
            pinterest: 'fab fa-pinterest',
            reddit: 'fab fa-reddit'
        };

        const statusClass = account.is_active ? 'account-active' : 'account-inactive';
        const statusText = account.is_active ? 'Connecté' : 'Déconnecté';

        return `
            <div class="account-card ${statusClass}" data-account-id="${account.id}">
                <div class="account-header">
                    <div class="account-icon">
                        <i class="${platformIcons[account.platform] || 'fas fa-share-alt'}"></i>
                    </div>
                    <div class="account-status">
                        <span class="status-indicator"></span>
                        ${statusText}
                    </div>
                </div>
                <div class="account-content">
                    <h4 class="account-name">${Utils.escapeHtml(account.account_name)}</h4>
                    <p class="account-platform">${account.platform.charAt(0).toUpperCase() + account.platform.slice(1)}</p>
                    <div class="account-stats">
                        <div class="stat">
                            <span class="stat-value">${account.followers_count || 0}</span>
                            <span class="stat-label">Abonnés</span>
                        </div>
                        <div class="stat">
                            <span class="stat-value">${account.scheduled_posts || 0}</span>
                            <span class="stat-label">Planifiées</span>
                        </div>
                    </div>
                </div>
                <div class="account-actions">
                    <button class="btn-icon" onclick="refreshAccount(${account.id})" title="Actualiser">
                        <i class="fas fa-sync"></i>
                    </button>
                    <button class="btn-icon text-error" onclick="disconnectAccount(${account.id})" title="Déconnecter">
                        <i class="fas fa-unlink"></i>
                    </button>
                </div>
            </div>
        `;
    }

    initUI() {
        // Initialiser les graphiques si Chart.js est disponible
        if (typeof Chart !== 'undefined') {
            this.initCharts();
        }

        // Autres initialisations UI
        this.initQuickActions();
    }

    initCharts() {
        // Cette méthode sera implémentée dans charts.js
        if (window.ChartsManager) {
            window.ChartsManager.init(this.stats);
        }
    }

    initQuickActions() {
        // Les actions rapides sont déjà gérées par les onclick dans le HTML
        // Cette méthode peut être étendue pour d'autres fonctionnalités
    }
}

// Fonctions globales pour les actions
function showCreatePostModal() {
    Utils.showAlert('Fonctionnalité en cours de développement', 'info');
}

function showAIAssistant() {
    Utils.showAlert('Assistant IA en cours de développement', 'info');
}

function showSocialAccounts() {
    Utils.showAlert('Gestion des comptes sociaux en cours de développement', 'info');
}

function editPost(postId) {
    Utils.showAlert(`Modification de la publication ${postId} en cours de développement`, 'info');
}

function duplicatePost(postId) {
    Utils.showAlert(`Duplication de la publication ${postId} en cours de développement`, 'info');
}

function deletePost(postId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette publication ?')) {
        Utils.showAlert(`Suppression de la publication ${postId} en cours de développement`, 'info');
    }
}

function refreshAccount(accountId) {
    Utils.showAlert(`Actualisation du compte ${accountId} en cours de développement`, 'info');
}

function disconnectAccount(accountId) {
    if (confirm('Êtes-vous sûr de vouloir déconnecter ce compte ?')) {
        Utils.showAlert(`Déconnexion du compte ${accountId} en cours de développement`, 'info');
    }
}

// Initialisation quand le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
    window.dashboardManager = new DashboardManager();
});

// Styles CSS additionnels pour le tableau de bord
if (!document.getElementById('dashboard-additional-styles')) {
    const style = document.createElement('style');
    style.id = 'dashboard-additional-styles';
    style.textContent = `
        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
            color: #6b7280;
        }
        
        .empty-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .empty-state h3 {
            margin-bottom: 0.5rem;
            color: #374151;
        }
        
        .empty-state p {
            margin-bottom: 1.5rem;
        }
        
        .post-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1rem;
            transition: all 0.2s;
        }
        
        .post-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }
        
        .post-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }
        
        .post-platforms i {
            margin-right: 0.5rem;
            color: #6b7280;
        }
        
        .post-status {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-weight: 500;
        }
        
        .status-draft { background: #f3f4f6; color: #374151; }
        .status-scheduled { background: #fef3c7; color: #92400e; }
        .status-published { background: #d1fae5; color: #065f46; }
        .status-failed { background: #fee2e2; color: #991b1b; }
        
        .post-title {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #111827;
        }
        
        .post-text {
            font-size: 0.75rem;
            color: #6b7280;
            line-height: 1.4;
            margin-bottom: 0.75rem;
        }
        
        .post-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .post-date {
            font-size: 0.75rem;
            color: #9ca3af;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .post-actions {
            display: flex;
            gap: 0.25rem;
        }
        
        .btn-icon {
            background: none;
            border: none;
            padding: 0.25rem;
            border-radius: 0.25rem;
            cursor: pointer;
            color: #6b7280;
            transition: all 0.2s;
        }
        
        .btn-icon:hover {
            background: #f3f4f6;
            color: #374151;
        }
        
        .account-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1rem;
            transition: all 0.2s;
        }
        
        .account-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .account-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }
        
        .account-icon {
            font-size: 1.5rem;
            color: #3b82f6;
        }
        
        .account-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .status-indicator {
            width: 0.5rem;
            height: 0.5rem;
            border-radius: 50%;
        }
        
        .account-active .status-indicator {
            background: #10b981;
        }
        
        .account-inactive .status-indicator {
            background: #ef4444;
        }
        
        .account-name {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .account-platform {
            font-size: 0.75rem;
            color: #6b7280;
            margin-bottom: 0.75rem;
        }
        
        .account-stats {
            display: flex;
            gap: 1rem;
            margin-bottom: 0.75rem;
        }
        
        .account-stats .stat {
            text-align: center;
        }
        
        .account-stats .stat-value {
            display: block;
            font-weight: 600;
            font-size: 0.875rem;
        }
        
        .account-stats .stat-label {
            font-size: 0.75rem;
            color: #6b7280;
        }
        
        .account-actions {
            display: flex;
            justify-content: flex-end;
            gap: 0.25rem;
        }
    `;
    document.head.appendChild(style);
}
