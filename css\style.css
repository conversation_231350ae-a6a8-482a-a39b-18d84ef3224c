/* Variables CSS */
:root {
    /* Couleurs principales */
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --primary-light: #60a5fa;
    --secondary-color: #6366f1;
    --accent-color: #f59e0b;
    
    /* Couleurs de statut */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #06b6d4;
    
    /* Couleurs neutres */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Typographie */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>I', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Espacement */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    
    /* Bordures */
    --border-radius-sm: 0.375rem;
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-full: 9999px;
    
    /* Ombres */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typographie */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--spacing-4);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-4);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

/* Boutons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-sm);
    font-weight: 500;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Variantes de boutons */
.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--white);
}

.btn-secondary {
    background-color: var(--white);
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--gray-50);
    border-color: var(--gray-400);
    color: var(--gray-800);
}

.btn-success {
    background-color: var(--success-color);
    color: var(--white);
    border-color: var(--success-color);
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--white);
    border-color: var(--warning-color);
}

.btn-error {
    background-color: var(--error-color);
    color: var(--white);
    border-color: var(--error-color);
}

/* Tailles de boutons */
.btn-sm {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-xs);
}

.btn-large {
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--font-size-lg);
}

.btn-full {
    width: 100%;
}

/* Boutons sociaux */
.btn-social {
    background-color: var(--white);
    color: var(--gray-700);
    border-color: var(--gray-300);
    justify-content: flex-start;
    padding-left: var(--spacing-4);
}

.btn-google:hover {
    background-color: #f8f9fa;
    border-color: #dadce0;
}

.btn-facebook {
    color: #1877f2;
}

.btn-facebook:hover {
    background-color: #f0f2ff;
    border-color: #1877f2;
    color: #1877f2;
}

/* Loader dans les boutons */
.btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.btn:disabled .btn-text {
    opacity: 0;
}

/* Formulaires */
.form {
    width: 100%;
}

.form-group {
    margin-bottom: var(--spacing-5);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4);
}

.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-2);
}

.form-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-base);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    background-color: var(--white);
    transition: all var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
    border-color: var(--error-color);
}

.form-icon {
    position: absolute;
    left: var(--spacing-3);
    color: var(--gray-400);
    z-index: 1;
}

.form-input-group .form-input {
    padding-left: var(--spacing-10);
}

.password-toggle {
    position: absolute;
    right: var(--spacing-3);
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-1);
}

.form-help {
    margin-top: var(--spacing-1);
}

.form-help small {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.form-error {
    margin-top: var(--spacing-1);
    font-size: var(--font-size-xs);
    color: var(--error-color);
}

/* Checkbox et radio */
.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    cursor: pointer;
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.checkbox {
    appearance: none;
    width: 1rem;
    height: 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    background-color: var(--white);
    cursor: pointer;
    position: relative;
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.checkbox:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: 0.75rem;
    font-weight: bold;
}

/* Header */
.header {
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-200);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.navbar {
    padding: var(--spacing-4) 0;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
    text-decoration: none;
}

.logo i {
    color: var(--primary-color);
}

.nav-list {
    display: flex;
    align-items: center;
    gap: var(--spacing-8);
    list-style: none;
}

.nav-link {
    font-weight: 500;
    color: var(--gray-600);
    transition: color var(--transition-fast);
}

.nav-link:hover {
    color: var(--gray-900);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 0.25rem;
    cursor: pointer;
}

.nav-toggle .bar {
    width: 1.5rem;
    height: 0.125rem;
    background-color: var(--gray-600);
    transition: all var(--transition-fast);
}

/* Hero Section */
.hero {
    padding: var(--spacing-20) 0;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--spacing-6);
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin-bottom: var(--spacing-8);
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-12);
}

.hero-stats {
    display: flex;
    gap: var(--spacing-8);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

/* Dashboard Preview */
.dashboard-preview {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: transform var(--transition-slow);
}

.dashboard-preview:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

.preview-header {
    background-color: var(--gray-100);
    padding: var(--spacing-3);
    border-bottom: 1px solid var(--gray-200);
}

.preview-dots {
    display: flex;
    gap: var(--spacing-2);
}

.preview-dots span {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: var(--border-radius-full);
    background-color: var(--gray-300);
}

.preview-dots span:first-child {
    background-color: var(--error-color);
}

.preview-dots span:nth-child(2) {
    background-color: var(--warning-color);
}

.preview-dots span:last-child {
    background-color: var(--success-color);
}

.preview-content {
    display: flex;
    height: 300px;
}

.preview-sidebar {
    width: 200px;
    background-color: var(--gray-50);
    border-right: 1px solid var(--gray-200);
    padding: var(--spacing-4);
}

.sidebar-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--spacing-1);
}

.sidebar-item.active {
    background-color: var(--primary-color);
    color: var(--white);
}

.preview-main {
    flex: 1;
    padding: var(--spacing-4);
}

.post-card {
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: var(--spacing-4);
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-3);
}

.social-icons {
    display: flex;
    gap: var(--spacing-2);
}

.social-icons i {
    color: var(--gray-400);
}

.post-status {
    font-size: var(--font-size-xs);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-full);
    font-weight: 500;
}

.post-status.scheduled {
    background-color: var(--warning-color);
    color: var(--white);
}

.post-content p {
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    margin-bottom: var(--spacing-3);
}

.post-footer {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-4);
    }
    
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
        text-align: center;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .dashboard-preview {
        transform: none;
    }
    
    .preview-content {
        flex-direction: column;
        height: auto;
    }
    
    .preview-sidebar {
        width: 100%;
    }
    
    .sidebar-item {
        display: inline-flex;
        margin-right: var(--spacing-2);
    }
}

/* Utilitaires */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-info { color: var(--info-color); }

.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-error { background-color: var(--error-color); }
.bg-info { background-color: var(--info-color); }

.hidden { display: none !important; }
.visible { display: block !important; }

.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

.spin {
    animation: spin 1s linear infinite;
}

/* Skeleton loading */
.skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.loading-skeleton {
    display: flex;
    gap: var(--spacing-4);
}

.skeleton-post,
.skeleton-account {
    height: 120px;
    border-radius: var(--border-radius);
    flex: 1;
}

/* Platforms Section */
.platforms {
    padding: var(--spacing-16) 0;
    background-color: var(--white);
}

.section-title {
    text-align: center;
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-12);
    color: var(--gray-900);
}

.section-description {
    text-align: center;
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin-bottom: var(--spacing-12);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.platforms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-6);
    max-width: 800px;
    margin: 0 auto;
}

.platform-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-6);
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.platform-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.platform-card i {
    font-size: var(--font-size-2xl);
    color: var(--gray-600);
    transition: color var(--transition-fast);
}

.platform-card:hover i {
    color: var(--primary-color);
}

.platform-card span {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
}

/* Features Section */
.features {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-8);
}

.feature-card {
    background-color: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    transition: all var(--transition-fast);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 3rem;
    height: 3rem;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-6);
    font-size: var(--font-size-xl);
}

.feature-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-4);
    color: var(--gray-900);
}

.feature-description {
    color: var(--gray-600);
    line-height: 1.6;
}

/* CTA Section */
.cta {
    padding: var(--spacing-20) 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    text-align: center;
}

.cta-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-6);
}

.cta-description {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-8);
    opacity: 0.9;
}

.cta .btn-primary {
    background-color: var(--white);
    color: var(--primary-color);
    border-color: var(--white);
}

.cta .btn-primary:hover {
    background-color: var(--gray-100);
    border-color: var(--gray-100);
}

/* Footer */
.footer {
    background-color: var(--gray-900);
    color: var(--gray-300);
    padding: var(--spacing-16) 0 var(--spacing-8);
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-12);
}

.footer-section h4 {
    color: var(--white);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-4);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-2);
}

.footer-section ul li a {
    color: var(--gray-400);
    transition: color var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--white);
}

.footer-brand p {
    color: var(--gray-400);
    margin-top: var(--spacing-4);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-8);
    border-top: 1px solid var(--gray-700);
}

.footer-links {
    display: flex;
    gap: var(--spacing-6);
}

.footer-links a {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--border-radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}
