<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comptes Sociaux - Social Scheduler</title>
    <meta name="description" content="Gérez vos comptes de réseaux sociaux connectés à Social Scheduler.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="index.html" class="logo">
                <i class="fas fa-calendar-alt"></i>
                <span>Social Scheduler</span>
            </a>
            <button class="sidebar-toggle" id="sidebar-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="dashboard.html" class="nav-link">
                        <i class="fas fa-home"></i>
                        <span>Tableau de bord</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="calendar.html" class="nav-link">
                        <i class="fas fa-calendar"></i>
                        <span>Calendrier</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="posts.html" class="nav-link">
                        <i class="fas fa-file-alt"></i>
                        <span>Publications</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showCreatePostModal()">
                        <i class="fas fa-plus"></i>
                        <span>Nouvelle publication</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="analytics.html" class="nav-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analyses</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="social-accounts.html" class="nav-link active">
                        <i class="fas fa-share-alt"></i>
                        <span>Comptes sociaux</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>Équipe</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-images"></i>
                        <span>Bibliothèque</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="settings.html" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Paramètres</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="sidebar-footer">
            <div class="user-info" id="user-info">
                <div class="user-avatar">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="Avatar" id="user-avatar">
                </div>
                <div class="user-details">
                    <div class="user-name" id="user-name">Chargement...</div>
                    <div class="user-plan" id="user-plan">Plan Free</div>
                </div>
                <div class="user-menu">
                    <button class="user-menu-btn" onclick="toggleUserMenu()">
                        <i class="fas fa-chevron-up"></i>
                    </button>
                    <div class="user-menu-dropdown" id="user-menu-dropdown">
                        <a href="settings.html" class="menu-item">
                            <i class="fas fa-user"></i>
                            Profil
                        </a>
                        <a href="#" class="menu-item">
                            <i class="fas fa-crown"></i>
                            Upgrade
                        </a>
                        <a href="#" class="menu-item" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            Déconnexion
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-btn" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title">Comptes sociaux</h1>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" onclick="showConnectModal()">
                    <i class="fas fa-plus"></i>
                    Connecter un compte
                </button>
            </div>
        </header>

        <!-- Social Accounts Content -->
        <div class="social-accounts-content">
            <!-- Connected Accounts -->
            <div class="accounts-section">
                <div class="section-header">
                    <h2>Comptes connectés</h2>
                    <p>Gérez vos comptes de réseaux sociaux connectés</p>
                </div>
                
                <div class="connected-accounts" id="connected-accounts">
                    <!-- Connected accounts will be loaded here -->
                    <div class="loading-container">
                        <div class="loading-spinner">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                        <p>Chargement des comptes...</p>
                    </div>
                </div>
            </div>

            <!-- Available Platforms -->
            <div class="platforms-section">
                <div class="section-header">
                    <h2>Plateformes disponibles</h2>
                    <p>Connectez vos comptes sur ces plateformes</p>
                </div>
                
                <div class="available-platforms">
                    <div class="platform-card" data-platform="facebook">
                        <div class="platform-icon facebook">
                            <i class="fab fa-facebook-f"></i>
                        </div>
                        <div class="platform-info">
                            <h3>Facebook</h3>
                            <p>Pages et groupes Facebook</p>
                            <div class="platform-features">
                                <span class="feature">✓ Publications</span>
                                <span class="feature">✓ Stories</span>
                                <span class="feature">✓ Analyses</span>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="connectPlatform('facebook')">
                            Connecter
                        </button>
                    </div>

                    <div class="platform-card" data-platform="instagram">
                        <div class="platform-icon instagram">
                            <i class="fab fa-instagram"></i>
                        </div>
                        <div class="platform-info">
                            <h3>Instagram</h3>
                            <p>Comptes Instagram Business</p>
                            <div class="platform-features">
                                <span class="feature">✓ Posts</span>
                                <span class="feature">✓ Stories</span>
                                <span class="feature">✓ Reels</span>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="connectPlatform('instagram')">
                            Connecter
                        </button>
                    </div>

                    <div class="platform-card" data-platform="twitter">
                        <div class="platform-icon twitter">
                            <i class="fab fa-twitter"></i>
                        </div>
                        <div class="platform-info">
                            <h3>Twitter</h3>
                            <p>Comptes Twitter/X</p>
                            <div class="platform-features">
                                <span class="feature">✓ Tweets</span>
                                <span class="feature">✓ Threads</span>
                                <span class="feature">✓ Analyses</span>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="connectPlatform('twitter')">
                            Connecter
                        </button>
                    </div>

                    <div class="platform-card" data-platform="linkedin">
                        <div class="platform-icon linkedin">
                            <i class="fab fa-linkedin-in"></i>
                        </div>
                        <div class="platform-info">
                            <h3>LinkedIn</h3>
                            <p>Profils et pages LinkedIn</p>
                            <div class="platform-features">
                                <span class="feature">✓ Publications</span>
                                <span class="feature">✓ Articles</span>
                                <span class="feature">✓ Analyses</span>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="connectPlatform('linkedin')">
                            Connecter
                        </button>
                    </div>

                    <div class="platform-card" data-platform="tiktok">
                        <div class="platform-icon tiktok">
                            <i class="fab fa-tiktok"></i>
                        </div>
                        <div class="platform-info">
                            <h3>TikTok</h3>
                            <p>Comptes TikTok Business</p>
                            <div class="platform-features">
                                <span class="feature">✓ Vidéos</span>
                                <span class="feature">✓ Analyses</span>
                                <span class="feature">✓ Tendances</span>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="connectPlatform('tiktok')">
                            Connecter
                        </button>
                    </div>

                    <div class="platform-card" data-platform="youtube">
                        <div class="platform-icon youtube">
                            <i class="fab fa-youtube"></i>
                        </div>
                        <div class="platform-info">
                            <h3>YouTube</h3>
                            <p>Chaînes YouTube</p>
                            <div class="platform-features">
                                <span class="feature">✓ Vidéos</span>
                                <span class="feature">✓ Shorts</span>
                                <span class="feature">✓ Analyses</span>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="connectPlatform('youtube')">
                            Connecter
                        </button>
                    </div>

                    <div class="platform-card" data-platform="pinterest">
                        <div class="platform-icon pinterest">
                            <i class="fab fa-pinterest-p"></i>
                        </div>
                        <div class="platform-info">
                            <h3>Pinterest</h3>
                            <p>Comptes Pinterest Business</p>
                            <div class="platform-features">
                                <span class="feature">✓ Épingles</span>
                                <span class="feature">✓ Tableaux</span>
                                <span class="feature">✓ Analyses</span>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="connectPlatform('pinterest')">
                            Connecter
                        </button>
                    </div>

                    <div class="platform-card" data-platform="reddit">
                        <div class="platform-icon reddit">
                            <i class="fab fa-reddit-alien"></i>
                        </div>
                        <div class="platform-info">
                            <h3>Reddit</h3>
                            <p>Comptes Reddit</p>
                            <div class="platform-features">
                                <span class="feature">✓ Posts</span>
                                <span class="feature">✓ Commentaires</span>
                                <span class="feature">✓ Subreddits</span>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="connectPlatform('reddit')">
                            Connecter
                        </button>
                    </div>
                </div>
            </div>

            <!-- Help Section -->
            <div class="help-section">
                <div class="help-card">
                    <div class="help-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="help-content">
                        <h3>Besoin d'aide ?</h3>
                        <p>Consultez notre guide pour connecter vos comptes de réseaux sociaux en toute sécurité.</p>
                        <a href="#" class="btn btn-secondary">
                            <i class="fas fa-book"></i>
                            Guide de connexion
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Connect Platform Modal -->
    <div id="connect-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="connect-modal-title">Connecter un compte</h3>
                <button class="modal-close" onclick="closeConnectModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="connect-modal-body">
                <!-- Connection content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Account Details Modal -->
    <div id="account-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Détails du compte</h3>
                <button class="modal-close" onclick="closeAccountModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="account-modal-body">
                <!-- Account details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeAccountModal()">Fermer</button>
                <button class="btn btn-warning" onclick="refreshAccount()">
                    <i class="fas fa-sync"></i>
                    Actualiser
                </button>
                <button class="btn btn-error" onclick="disconnectAccount()">
                    <i class="fas fa-unlink"></i>
                    Déconnecter
                </button>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div id="alert-container"></div>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/social-accounts.js"></script>
</body>
</html>
