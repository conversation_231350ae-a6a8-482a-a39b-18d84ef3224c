<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API - Social Scheduler</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Test API - Social Scheduler</h1>
    
    <!-- Section de connexion -->
    <div class="test-section">
        <h2>1. Connexion</h2>
        <div class="form-group">
            <label>Email:</label>
            <input type="email" id="login-email" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label>Mot de passe:</label>
            <input type="password" id="login-password" value="password">
        </div>
        <button onclick="testLogin()">Se connecter</button>
        <div id="login-result" class="result" style="display: none;"></div>
    </div>

    <!-- Section comptes sociaux -->
    <div class="test-section">
        <h2>2. Comptes sociaux</h2>
        <button onclick="testListAccounts()">Lister les comptes</button>
        <button onclick="testConnectAccount()">Connecter Facebook</button>
        <button onclick="testConnectInstagram()">Connecter Instagram</button>
        <div id="accounts-result" class="result" style="display: none;"></div>
    </div>

    <!-- Section publications -->
    <div class="test-section">
        <h2>3. Publications</h2>
        <div class="form-group">
            <label>Titre:</label>
            <input type="text" id="post-title" value="Test de publication">
        </div>
        <div class="form-group">
            <label>Contenu:</label>
            <textarea id="post-content" rows="4">Ceci est un test de publication depuis l'API Social Scheduler. #test #api</textarea>
        </div>
        <div class="form-group">
            <label>Statut:</label>
            <select id="post-status">
                <option value="draft">Brouillon</option>
                <option value="scheduled">Planifié</option>
            </select>
        </div>
        <div class="form-group">
            <label>Date de planification (si planifié):</label>
            <input type="datetime-local" id="post-scheduled">
        </div>
        <button onclick="testCreatePost()">Créer publication</button>
        <button onclick="testListPosts()">Lister les publications</button>
        <div id="posts-result" class="result" style="display: none;"></div>
    </div>

    <!-- Section analyses -->
    <div class="test-section">
        <h2>4. Analyses</h2>
        <button onclick="testAnalytics()">Charger les analyses</button>
        <div id="analytics-result" class="result" style="display: none;"></div>
    </div>

    <script>
        let authToken = localStorage.getItem('auth_token');

        // Fonction utilitaire pour les appels API
        async function apiCall(endpoint, options = {}) {
            const url = `api/${endpoint}`;
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': authToken ? `Bearer ${authToken}` : ''
                }
            };

            const finalOptions = { ...defaultOptions, ...options };
            
            try {
                const response = await fetch(url, finalOptions);
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // Test de connexion
        async function testLogin() {
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            
            const result = await apiCall('auth/login.php', {
                method: 'POST',
                body: JSON.stringify({ email, password })
            });

            const resultDiv = document.getElementById('login-result');
            resultDiv.style.display = 'block';
            
            if (result.success && result.data.success) {
                authToken = result.data.data.token;
                localStorage.setItem('auth_token', authToken);
                localStorage.setItem('user_data', JSON.stringify(result.data.data.user));
                
                resultDiv.className = 'result success';
                resultDiv.textContent = 'Connexion réussie!\n' + JSON.stringify(result.data, null, 2);
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'Erreur de connexion:\n' + JSON.stringify(result, null, 2);
            }
        }

        // Test liste des comptes
        async function testListAccounts() {
            const result = await apiCall('social/connect.php');
            showResult('accounts-result', result);
        }

        // Test connexion Facebook
        async function testConnectAccount() {
            const result = await apiCall('social/connect.php', {
                method: 'POST',
                body: JSON.stringify({ 
                    action: 'connect',
                    platform: 'facebook'
                })
            });
            showResult('accounts-result', result);
        }

        // Test connexion Instagram
        async function testConnectInstagram() {
            const result = await apiCall('social/connect.php', {
                method: 'POST',
                body: JSON.stringify({ 
                    action: 'connect',
                    platform: 'instagram'
                })
            });
            showResult('accounts-result', result);
        }

        // Test création de publication
        async function testCreatePost() {
            const title = document.getElementById('post-title').value;
            const content = document.getElementById('post-content').value;
            const status = document.getElementById('post-status').value;
            const scheduledAt = document.getElementById('post-scheduled').value;

            const postData = {
                title,
                content,
                status,
                hashtags: '#test #api',
                platforms: []
            };

            if (status === 'scheduled' && scheduledAt) {
                postData.scheduled_at = scheduledAt;
            }

            const result = await apiCall('posts/create_simple.php', {
                method: 'POST',
                body: JSON.stringify(postData)
            });
            showResult('posts-result', result);
        }

        // Test liste des publications
        async function testListPosts() {
            const result = await apiCall('posts/list.php');
            showResult('posts-result', result);
        }

        // Test analyses
        async function testAnalytics() {
            const result = await apiCall('analytics/stats.php?metric=overview&period=30d');
            showResult('analytics-result', result);
        }

        // Fonction utilitaire pour afficher les résultats
        function showResult(elementId, result) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.style.display = 'block';
            
            if (result.success) {
                resultDiv.className = 'result success';
            } else {
                resultDiv.className = 'result error';
            }
            
            resultDiv.textContent = JSON.stringify(result, null, 2);
        }

        // Initialiser la date de planification par défaut
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            now.setHours(now.getHours() + 1); // 1 heure dans le futur
            document.getElementById('post-scheduled').value = now.toISOString().slice(0, 16);
        });
    </script>
</body>
</html>
