<?php
/**
 * Configuration de la base de données
 * Application de planification de médias sociaux
 */

// Activer l'affichage des erreurs pour le débogage (à désactiver en production)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

class Database {
    private $host = 'localhost';
    private $db_name = 'social_scheduler';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    public $conn;

    public function getConnection() {
        $this->conn = null;

        try {
            // Vérifier si l'extension PDO est chargée
            if (!extension_loaded('pdo')) {
                throw new Exception("L'extension PDO n'est pas installée");
            }

            if (!extension_loaded('pdo_mysql')) {
                throw new Exception("L'extension PDO MySQL n'est pas installée");
            }

            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $this->conn = new PDO($dsn, $this->username, $this->password, $options);

        } catch(PDOException $exception) {
            error_log("Erreur de connexion PDO: " . $exception->getMessage());

            // Essayer de créer la base de données si elle n'existe pas
            try {
                $dsn_without_db = "mysql:host=" . $this->host . ";charset=" . $this->charset;
                $temp_conn = new PDO($dsn_without_db, $this->username, $this->password, $options);
                $temp_conn->exec("CREATE DATABASE IF NOT EXISTS `" . $this->db_name . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

                // Reconnecter avec la base de données créée
                $this->conn = new PDO($dsn, $this->username, $this->password, $options);

            } catch(PDOException $create_exception) {
                error_log("Erreur de création de base de données: " . $create_exception->getMessage());
                throw new Exception("Erreur de connexion à la base de données: " . $exception->getMessage());
            }
        } catch(Exception $exception) {
            error_log("Erreur générale: " . $exception->getMessage());
            throw new Exception($exception->getMessage());
        }

        return $this->conn;
    }
}

/**
 * Classe utilitaire pour les opérations de base de données
 */
class DatabaseHelper {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }

    /**
     * Exécute une requête préparée
     */
    public function executeQuery($query, $params = []) {
        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute($params);
            return $stmt;
        } catch(PDOException $e) {
            error_log("Erreur SQL: " . $e->getMessage());
            throw new Exception("Erreur lors de l'exécution de la requête");
        }
    }

    /**
     * Récupère un seul enregistrement
     */
    public function fetchOne($query, $params = []) {
        $stmt = $this->executeQuery($query, $params);
        return $stmt->fetch();
    }

    /**
     * Récupère tous les enregistrements
     */
    public function fetchAll($query, $params = []) {
        $stmt = $this->executeQuery($query, $params);
        return $stmt->fetchAll();
    }

    /**
     * Insère un enregistrement et retourne l'ID
     */
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $query = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->executeQuery($query, $data);
        
        return $this->conn->lastInsertId();
    }

    /**
     * Met à jour un enregistrement
     */
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $key) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);
        
        $query = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        return $this->executeQuery($query, $params);
    }

    /**
     * Supprime un enregistrement
     */
    public function delete($table, $where, $params = []) {
        $query = "DELETE FROM {$table} WHERE {$where}";
        return $this->executeQuery($query, $params);
    }

    /**
     * Commence une transaction
     */
    public function beginTransaction() {
        return $this->conn->beginTransaction();
    }

    /**
     * Valide une transaction
     */
    public function commit() {
        return $this->conn->commit();
    }

    /**
     * Annule une transaction
     */
    public function rollback() {
        return $this->conn->rollback();
    }
}

/**
 * Configuration générale de l'application
 */
class Config {
    // Clés API pour les médias sociaux
    const FACEBOOK_APP_ID = 'your_facebook_app_id';
    const FACEBOOK_APP_SECRET = 'your_facebook_app_secret';
    
    const TWITTER_API_KEY = 'your_twitter_api_key';
    const TWITTER_API_SECRET = 'your_twitter_api_secret';
    
    const LINKEDIN_CLIENT_ID = 'your_linkedin_client_id';
    const LINKEDIN_CLIENT_SECRET = 'your_linkedin_client_secret';
    
    const INSTAGRAM_CLIENT_ID = 'your_instagram_client_id';
    const INSTAGRAM_CLIENT_SECRET = 'your_instagram_client_secret';
    
    // Configuration OpenAI pour l'IA
    const OPENAI_API_KEY = 'your_openai_api_key';
    
    // Configuration JWT
    const JWT_SECRET = 'your_jwt_secret_key_here';
    const JWT_EXPIRATION = 86400; // 24 heures
    
    // Configuration de l'application
    const APP_NAME = 'Social Scheduler';
    const APP_VERSION = '1.0.0';
    const APP_URL = 'http://localhost';
    
    // Configuration email
    const SMTP_HOST = 'localhost';
    const SMTP_PORT = 587;
    const SMTP_USERNAME = '';
    const SMTP_PASSWORD = '';
    const FROM_EMAIL = '<EMAIL>';
    const FROM_NAME = 'Social Scheduler';
    
    // Limites par plan
    const LIMITS = [
        'free' => [
            'posts_per_month' => 30,
            'social_accounts' => 3,
            'team_members' => 1
        ],
        'pro' => [
            'posts_per_month' => 300,
            'social_accounts' => 10,
            'team_members' => 5
        ],
        'enterprise' => [
            'posts_per_month' => -1, // illimité
            'social_accounts' => -1,
            'team_members' => -1
        ]
    ];
}

// Configuration des en-têtes CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=UTF-8');

// Gestion des requêtes OPTIONS pour CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}
?>
