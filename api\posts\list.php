<?php
require_once '../config/database.php';
require_once '../config/auth.php';

/**
 * API de listage des publications
 */

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
    exit();
}

try {
    $auth = new Auth();
    $permissions = new Permissions();
    $db = new DatabaseHelper();
    
    // Vérification de l'authentification
    $user = $auth->requireAuth();
    
    // Paramètres de requête
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(50, max(1, (int)($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    $status = $_GET['status'] ?? 'all';
    $teamId = $_GET['team_id'] ?? null;
    $platform = $_GET['platform'] ?? null;
    $search = $_GET['search'] ?? '';
    $sortBy = $_GET['sort_by'] ?? 'created_at';
    $sortOrder = $_GET['sort_order'] ?? 'DESC';
    
    // Validation des paramètres
    $validStatuses = ['all', 'draft', 'scheduled', 'published', 'failed'];
    if (!in_array($status, $validStatuses)) {
        $status = 'all';
    }
    
    $validSortFields = ['created_at', 'scheduled_at', 'published_at', 'title'];
    if (!in_array($sortBy, $validSortFields)) {
        $sortBy = 'created_at';
    }
    
    $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';
    
    // Vérification des permissions pour l'équipe
    if ($teamId && !$permissions->canAccessTeam($user['id'], $teamId)) {
        throw new Exception('Vous n\'avez pas accès à cette équipe');
    }
    
    // Construction de la requête
    $whereConditions = ['p.user_id = ?'];
    $params = [$user['id']];
    
    // Filtre par équipe
    if ($teamId) {
        $whereConditions[] = 'p.team_id = ?';
        $params[] = $teamId;
    }
    
    // Filtre par statut
    if ($status !== 'all') {
        $whereConditions[] = 'p.status = ?';
        $params[] = $status;
    }
    
    // Filtre par recherche
    if (!empty($search)) {
        $whereConditions[] = '(p.title LIKE ? OR p.content LIKE ?)';
        $searchTerm = '%' . $search . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    // Filtre par plateforme
    if ($platform) {
        $whereConditions[] = 'EXISTS (
            SELECT 1 FROM scheduled_posts sp 
            JOIN social_accounts sa ON sp.social_account_id = sa.id 
            WHERE sp.post_id = p.id AND sa.platform = ?
        )';
        $params[] = $platform;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // Requête pour compter le total
    $totalQuery = "
        SELECT COUNT(*) as total 
        FROM posts p 
        LEFT JOIN teams t ON p.team_id = t.id 
        WHERE {$whereClause}
    ";
    
    $totalResult = $db->fetchOne($totalQuery, $params);
    $total = (int)$totalResult['total'];
    
    // Requête principale
    $query = "
        SELECT p.*, 
               u.username, u.first_name, u.last_name, u.avatar_url,
               t.name as team_name,
               (SELECT COUNT(*) FROM scheduled_posts WHERE post_id = p.id) as scheduled_count,
               (SELECT COUNT(*) FROM scheduled_posts WHERE post_id = p.id AND status = 'published') as published_count
        FROM posts p
        JOIN users u ON p.user_id = u.id
        LEFT JOIN teams t ON p.team_id = t.id
        WHERE {$whereClause}
        ORDER BY p.{$sortBy} {$sortOrder}
        LIMIT {$limit} OFFSET {$offset}
    ";
    
    $posts = $db->fetchAll($query, $params);
    
    // Enrichissement des données
    foreach ($posts as &$post) {
        // Décodage des JSON
        $post['media_urls'] = json_decode($post['media_urls'] ?? '[]', true);
        $post['hashtags'] = json_decode($post['hashtags'] ?? '[]', true);
        
        // Comptes sociaux planifiés
        $post['scheduled_accounts'] = $db->fetchAll(
            "SELECT sp.*, sa.platform, sa.account_name, sa.profile_picture
             FROM scheduled_posts sp
             JOIN social_accounts sa ON sp.social_account_id = sa.id
             WHERE sp.post_id = ?
             ORDER BY sa.platform",
            [$post['id']]
        );
        
        // Statistiques d'engagement si publié
        if ($post['status'] === 'published') {
            $analytics = $db->fetchOne(
                "SELECT 
                    SUM(likes_count) as total_likes,
                    SUM(comments_count) as total_comments,
                    SUM(shares_count) as total_shares,
                    SUM(views_count) as total_views,
                    AVG(engagement_rate) as avg_engagement
                 FROM analytics 
                 WHERE post_id = ?",
                [$post['id']]
            );
            
            $post['analytics'] = [
                'total_likes' => (int)($analytics['total_likes'] ?? 0),
                'total_comments' => (int)($analytics['total_comments'] ?? 0),
                'total_shares' => (int)($analytics['total_shares'] ?? 0),
                'total_views' => (int)($analytics['total_views'] ?? 0),
                'avg_engagement' => round($analytics['avg_engagement'] ?? 0, 2)
            ];
        }
    }
    
    // Calcul de la pagination
    $totalPages = ceil($total / $limit);
    $hasNext = $page < $totalPages;
    $hasPrev = $page > 1;
    
    // Statistiques générales
    $stats = getPostsStats($db, $user['id'], $teamId);
    
    // Réponse de succès
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'data' => [
            'posts' => $posts,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_items' => $total,
                'items_per_page' => $limit,
                'has_next' => $hasNext,
                'has_prev' => $hasPrev
            ],
            'stats' => $stats,
            'filters' => [
                'status' => $status,
                'team_id' => $teamId,
                'platform' => $platform,
                'search' => $search
            ]
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Récupère les statistiques des publications
 */
function getPostsStats($db, $userId, $teamId = null) {
    $whereClause = 'user_id = ?';
    $params = [$userId];
    
    if ($teamId) {
        $whereClause .= ' AND team_id = ?';
        $params[] = $teamId;
    }
    
    // Statistiques par statut
    $statusStats = $db->fetchAll(
        "SELECT status, COUNT(*) as count 
         FROM posts 
         WHERE {$whereClause} 
         GROUP BY status",
        $params
    );
    
    // Statistiques par plateforme
    $platformStats = $db->fetchAll(
        "SELECT sa.platform, COUNT(DISTINCT sp.post_id) as post_count
         FROM scheduled_posts sp
         JOIN social_accounts sa ON sp.social_account_id = sa.id
         JOIN posts p ON sp.post_id = p.id
         WHERE p.{$whereClause}
         GROUP BY sa.platform
         ORDER BY post_count DESC",
        $params
    );
    
    // Publications récentes
    $recentActivity = $db->fetchAll(
        "SELECT DATE(created_at) as date, COUNT(*) as count
         FROM posts
         WHERE {$whereClause} AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
         GROUP BY DATE(created_at)
         ORDER BY date DESC
         LIMIT 30",
        $params
    );
    
    return [
        'by_status' => $statusStats,
        'by_platform' => $platformStats,
        'recent_activity' => $recentActivity
    ];
}
?>
