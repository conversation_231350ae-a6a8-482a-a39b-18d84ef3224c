<?php
require_once '../config/database.php';
require_once '../config/auth.php';

/**
 * API de connexion des utilisateurs
 */

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
    exit();
}

try {
    // Récupération des données JSON
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Données JSON invalides');
    }
    
    // Validation des champs requis
    if (empty($input['login']) || empty($input['password'])) {
        throw new Exception('Email/nom d\'utilisateur et mot de passe requis');
    }
    
    $login = trim($input['login']); // Peut être email ou username
    $password = $input['password'];
    $rememberMe = $input['remember_me'] ?? false;
    
    $auth = new Auth();
    $db = new DatabaseHelper();
    
    // Recherche de l'utilisateur par email ou nom d'utilisateur
    $user = $db->fetchOne(
        "SELECT id, username, email, password_hash, first_name, last_name, avatar_url, 
                plan_type, timezone, language, is_active, email_verified 
         FROM users 
         WHERE (email = ? OR username = ?) AND is_active = 1",
        [$login, $login]
    );
    
    if (!$user) {
        throw new Exception('Identifiants incorrects');
    }
    
    // Vérification du mot de passe
    if (!$auth->verifyPassword($password, $user['password_hash'])) {
        // Log de tentative de connexion échouée
        error_log("Tentative de connexion échouée pour : {$login}");
        throw new Exception('Identifiants incorrects');
    }
    
    // Vérification si l'email est vérifié (optionnel)
    if (!$user['email_verified']) {
        // Pour le moment, on permet la connexion même sans vérification
        // throw new Exception('Veuillez vérifier votre email avant de vous connecter');
    }
    
    // Calcul de la durée d'expiration
    $expiration = $rememberMe ? (30 * 24 * 60 * 60) : Config::JWT_EXPIRATION; // 30 jours ou 24h
    
    // Génération du token JWT
    $token = $auth->generateJWT($user['id'], $user['email']);
    
    // Génération du token de session
    $sessionToken = $auth->generateSessionToken();
    $expiresAt = date('Y-m-d H:i:s', time() + $expiration);
    
    // Sauvegarde de la session
    $auth->saveSession($user['id'], $sessionToken, $expiresAt);
    
    // Mise à jour de la dernière connexion
    $db->update(
        'users',
        ['updated_at' => date('Y-m-d H:i:s')],
        'id = ?',
        [$user['id']]
    );
    
    // Récupération des statistiques utilisateur
    $stats = getUserStats($db, $user['id']);
    
    // Récupération des équipes de l'utilisateur
    $teams = getUserTeams($db, $user['id']);
    
    // Suppression du hash du mot de passe de la réponse
    unset($user['password_hash']);
    
    // Log de connexion réussie
    error_log("Connexion réussie pour : {$user['email']}");
    
    // Réponse de succès
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'message' => 'Connexion réussie',
        'data' => [
            'user' => $user,
            'token' => $token,
            'session_token' => $sessionToken,
            'expires_at' => $expiresAt,
            'stats' => $stats,
            'teams' => $teams
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Récupère les statistiques de l'utilisateur
 */
function getUserStats($db, $userId) {
    // Nombre total de publications
    $totalPosts = $db->fetchOne(
        "SELECT COUNT(*) as count FROM posts WHERE user_id = ?",
        [$userId]
    );
    
    // Publications planifiées
    $scheduledPosts = $db->fetchOne(
        "SELECT COUNT(*) as count FROM posts WHERE user_id = ? AND status = 'scheduled'",
        [$userId]
    );
    
    // Publications publiées ce mois
    $publishedThisMonth = $db->fetchOne(
        "SELECT COUNT(*) as count FROM posts 
         WHERE user_id = ? AND status = 'published' 
         AND published_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)",
        [$userId]
    );
    
    // Comptes sociaux connectés
    $socialAccounts = $db->fetchOne(
        "SELECT COUNT(*) as count FROM social_accounts WHERE user_id = ? AND is_active = 1",
        [$userId]
    );
    
    // Engagement moyen du mois dernier
    $avgEngagement = $db->fetchOne(
        "SELECT AVG(engagement_rate) as avg_rate FROM analytics a
         JOIN posts p ON a.post_id = p.id
         WHERE p.user_id = ? AND a.recorded_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)",
        [$userId]
    );
    
    return [
        'total_posts' => (int)$totalPosts['count'],
        'scheduled_posts' => (int)$scheduledPosts['count'],
        'published_this_month' => (int)$publishedThisMonth['count'],
        'social_accounts' => (int)$socialAccounts['count'],
        'avg_engagement_rate' => round($avgEngagement['avg_rate'] ?? 0, 2)
    ];
}

/**
 * Récupère les équipes de l'utilisateur
 */
function getUserTeams($db, $userId) {
    return $db->fetchAll(
        "SELECT t.id, t.name, t.description, tm.role, 
                (SELECT COUNT(*) FROM team_members WHERE team_id = t.id) as member_count
         FROM teams t
         JOIN team_members tm ON t.id = tm.team_id
         WHERE tm.user_id = ?
         ORDER BY t.name",
        [$userId]
    );
}
?>
