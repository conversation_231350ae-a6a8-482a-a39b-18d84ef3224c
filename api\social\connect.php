<?php
require_once '../config/database.php';
require_once '../config/auth.php';

/**
 * API de connexion des comptes de médias sociaux
 */

// Headers CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=UTF-8');

// Gestion des requêtes OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Accepter GET et POST
if (!in_array($_SERVER['REQUEST_METHOD'], ['GET', 'POST'])) {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
    exit();
}

try {
    // Vérifier l'authentification
    $auth = new Auth();
    $user = $auth->getCurrentUser();

    if (!$user) {
        throw new Exception('Authentification requise');
    }

    $database = new Database();
    $db = $database->getConnection();

    $method = $_SERVER['REQUEST_METHOD'];

    if ($method === 'GET') {
        // Lister les comptes connectés
        $stmt = $db->prepare("
            SELECT sa.*, COUNT(sp.id) as scheduled_posts,
                   COALESCE(SUM(CASE WHEN p.status = 'published' THEN 1 ELSE 0 END), 0) as published_posts
            FROM social_accounts sa
            LEFT JOIN scheduled_posts sp ON sa.id = sp.social_account_id
            LEFT JOIN posts p ON sp.post_id = p.id
            WHERE sa.user_id = ?
            GROUP BY sa.id
            ORDER BY sa.connected_at DESC
        ");
        $stmt->execute([$user['id']]);
        $accounts = $stmt->fetchAll();

        // Ajouter des données simulées si aucun compte
        if (empty($accounts)) {
            $accounts = [
                [
                    'id' => 1,
                    'platform' => 'facebook',
                    'account_name' => 'Ma Page Facebook',
                    'account_id' => 'fb_demo_123',
                    'followers_count' => 1250,
                    'is_active' => 1,
                    'connected_at' => date('Y-m-d H:i:s'),
                    'last_sync' => date('Y-m-d H:i:s'),
                    'scheduled_posts' => 3,
                    'published_posts' => 12,
                    'profile_picture' => 'https://images.unsplash.com/photo-*************-80b023f02d71?w=50&h=50&fit=crop&crop=face'
                ],
                [
                    'id' => 2,
                    'platform' => 'instagram',
                    'account_name' => '@mon_instagram',
                    'account_id' => 'ig_demo_456',
                    'followers_count' => 2340,
                    'is_active' => 1,
                    'connected_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                    'last_sync' => date('Y-m-d H:i:s', strtotime('-1 hour')),
                    'scheduled_posts' => 2,
                    'published_posts' => 8,
                    'profile_picture' => 'https://images.unsplash.com/photo-*************-80b023f02d71?w=50&h=50&fit=crop&crop=face'
                ]
            ];
        }

        echo json_encode([
            'success' => true,
            'data' => ['accounts' => $accounts]
        ]);
        exit();
    }

    // Pour les requêtes POST
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? 'connect';

    switch ($action) {
        case 'list':
            // Même logique que GET
            $stmt = $db->prepare("
                SELECT sa.*, COUNT(sp.id) as scheduled_posts,
                       COALESCE(SUM(CASE WHEN p.status = 'published' THEN 1 ELSE 0 END), 0) as published_posts
                FROM social_accounts sa
                LEFT JOIN scheduled_posts sp ON sa.id = sp.social_account_id
                LEFT JOIN posts p ON sp.post_id = p.id
                WHERE sa.user_id = ?
                GROUP BY sa.id
                ORDER BY sa.connected_at DESC
            ");
            $stmt->execute([$user['id']]);
            $accounts = $stmt->fetchAll();

            // Ajouter des données simulées si aucun compte
            if (empty($accounts)) {
                $accounts = [
                    [
                        'id' => 1,
                        'platform' => 'facebook',
                        'account_name' => 'Ma Page Facebook',
                        'account_id' => 'fb_demo_123',
                        'followers_count' => 1250,
                        'is_active' => 1,
                        'connected_at' => date('Y-m-d H:i:s'),
                        'last_sync' => date('Y-m-d H:i:s'),
                        'scheduled_posts' => 3,
                        'published_posts' => 12,
                        'profile_picture' => 'https://images.unsplash.com/photo-*************-80b023f02d71?w=50&h=50&fit=crop&crop=face'
                    ],
                    [
                        'id' => 2,
                        'platform' => 'instagram',
                        'account_name' => '@mon_instagram',
                        'account_id' => 'ig_demo_456',
                        'followers_count' => 2340,
                        'is_active' => 1,
                        'connected_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                        'last_sync' => date('Y-m-d H:i:s', strtotime('-1 hour')),
                        'scheduled_posts' => 2,
                        'published_posts' => 8,
                        'profile_picture' => 'https://images.unsplash.com/photo-*************-80b023f02d71?w=50&h=50&fit=crop&crop=face'
                    ]
                ];
            }

            echo json_encode([
                'success' => true,
                'data' => ['accounts' => $accounts]
            ]);
            break;

        case 'connect':
            // Simuler la connexion d'un nouveau compte
            $platform = $input['platform'] ?? '';

            if (empty($platform)) {
                throw new Exception('Plateforme requise');
            }

            // Générer des données simulées pour le nouveau compte
            $account_names = [
                'facebook' => 'Ma Page Facebook',
                'instagram' => '@mon_instagram',
                'twitter' => '@mon_twitter',
                'linkedin' => 'Mon LinkedIn',
                'tiktok' => '@mon_tiktok',
                'youtube' => 'Ma Chaîne YouTube',
                'pinterest' => 'Mon Pinterest',
                'reddit' => 'Mon Reddit'
            ];

            $account_name = $account_names[$platform] ?? "Compte $platform";
            $account_id = $platform . '_' . uniqid();
            $followers_count = rand(500, 5000);

            // Insérer le nouveau compte dans la base de données
            $stmt = $db->prepare("
                INSERT INTO social_accounts
                (user_id, platform, account_name, account_id, followers_count, is_active, connected_at)
                VALUES (?, ?, ?, ?, ?, 1, NOW())
            ");
            $stmt->execute([$user['id'], $platform, $account_name, $account_id, $followers_count]);

            $new_account_id = $db->lastInsertId();

            echo json_encode([
                'success' => true,
                'message' => 'Compte connecté avec succès',
                'data' => [
                    'account' => [
                        'id' => $new_account_id,
                        'platform' => $platform,
                        'account_name' => $account_name,
                        'account_id' => $account_id,
                        'followers_count' => $followers_count,
                        'is_active' => 1,
                        'connected_at' => date('Y-m-d H:i:s'),
                        'scheduled_posts' => 0,
                        'published_posts' => 0
                    ]
                ]
            ]);
            break;

        case 'disconnect':
            // Déconnecter un compte
            $account_id = $input['account_id'] ?? 0;

            $stmt = $db->prepare("
                DELETE FROM social_accounts
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$account_id, $user['id']]);

            echo json_encode([
                'success' => true,
                'message' => 'Compte déconnecté avec succès'
            ]);
            break;

        case 'refresh':
            // Actualiser un compte
            $account_id = $input['account_id'] ?? 0;

            // Simuler l'actualisation
            $new_followers = rand(500, 10000);

            $stmt = $db->prepare("
                UPDATE social_accounts
                SET followers_count = ?, updated_at = NOW()
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$new_followers, $account_id, $user['id']]);

            echo json_encode([
                'success' => true,
                'message' => 'Compte actualisé avec succès',
                'data' => ['followers_count' => $new_followers]
            ]);
            break;

        default:
            throw new Exception('Action non reconnue');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
    } else {
        // Création d'un nouveau compte
        $accountDbId = $db->insert('social_accounts', [
            'user_id' => $user['id'],
            'platform' => $platform,
            'account_name' => $accountName,
            'account_id' => $accountId,
            'access_token' => $accessToken,
            'refresh_token' => $refreshToken,
            'token_expires_at' => $tokenExpiresAt,
            'profile_picture' => $profilePicture,
            'followers_count' => $followersCount,
            'is_active' => 1
        ]);
        
        $message = 'Compte connecté avec succès';
    }
    
    // Récupération du compte créé/mis à jour
    $account = $db->fetchOne(
        "SELECT * FROM social_accounts WHERE id = ?",
        [$accountDbId]
    );
    
    // Test de la connexion
    $connectionTest = testSocialConnection($platform, $accessToken);
    
    return [
        'message' => $message,
        'account' => $account,
        'connection_test' => $connectionTest
    ];
}

/**
 * Déconnecte un compte de média social
 */
function disconnectSocialAccount($db, $user, $input) {
    $accountId = $input['account_id'] ?? null;
    
    if (!$accountId) {
        throw new Exception('ID du compte requis');
    }
    
    // Vérification que le compte appartient à l'utilisateur
    $account = $db->fetchOne(
        "SELECT * FROM social_accounts WHERE id = ? AND user_id = ?",
        [$accountId, $user['id']]
    );
    
    if (!$account) {
        throw new Exception('Compte non trouvé');
    }
    
    // Vérification s'il y a des publications planifiées
    $scheduledPosts = $db->fetchOne(
        "SELECT COUNT(*) as count FROM scheduled_posts 
         WHERE social_account_id = ? AND status = 'pending'",
        [$accountId]
    );
    
    if ($scheduledPosts['count'] > 0) {
        throw new Exception('Impossible de déconnecter : des publications sont planifiées sur ce compte');
    }
    
    // Désactivation du compte
    $db->update(
        'social_accounts',
        [
            'is_active' => 0,
            'access_token' => null,
            'refresh_token' => null,
            'updated_at' => date('Y-m-d H:i:s')
        ],
        'id = ?',
        [$accountId]
    );
    
    return [
        'message' => 'Compte déconnecté avec succès',
        'account_id' => $accountId
    ];
}

/**
 * Liste les comptes de médias sociaux de l'utilisateur
 */
function listSocialAccounts($db, $user) {
    $accounts = $db->fetchAll(
        "SELECT id, platform, account_name, account_id, profile_picture, 
                followers_count, is_active, token_expires_at, connected_at, updated_at
         FROM social_accounts 
         WHERE user_id = ? 
         ORDER BY platform, account_name",
        [$user['id']]
    );
    
    // Enrichissement avec les statistiques
    foreach ($accounts as &$account) {
        // Nombre de publications planifiées
        $scheduledCount = $db->fetchOne(
            "SELECT COUNT(*) as count FROM scheduled_posts 
             WHERE social_account_id = ? AND status = 'pending'",
            [$account['id']]
        );
        
        // Nombre de publications publiées
        $publishedCount = $db->fetchOne(
            "SELECT COUNT(*) as count FROM scheduled_posts 
             WHERE social_account_id = ? AND status = 'published'",
            [$account['id']]
        );
        
        $account['scheduled_posts'] = (int)$scheduledCount['count'];
        $account['published_posts'] = (int)$publishedCount['count'];
        
        // Vérification de l'expiration du token
        if ($account['token_expires_at']) {
            $account['token_expired'] = strtotime($account['token_expires_at']) <= time();
        } else {
            $account['token_expired'] = false;
        }
    }
    
    return [
        'accounts' => $accounts,
        'total_count' => count($accounts),
        'active_count' => count(array_filter($accounts, fn($a) => $a['is_active']))
    ];
}

/**
 * Rafraîchit un token d'accès
 */
function refreshSocialAccount($db, $user, $input) {
    $accountId = $input['account_id'] ?? null;
    
    if (!$accountId) {
        throw new Exception('ID du compte requis');
    }
    
    $account = $db->fetchOne(
        "SELECT * FROM social_accounts WHERE id = ? AND user_id = ?",
        [$accountId, $user['id']]
    );
    
    if (!$account) {
        throw new Exception('Compte non trouvé');
    }
    
    // Simulation du rafraîchissement du token
    $refreshResult = refreshAccessToken($account['platform'], $account['refresh_token']);
    
    if ($refreshResult['success']) {
        // Mise à jour du token
        $db->update(
            'social_accounts',
            [
                'access_token' => $refreshResult['access_token'],
                'token_expires_at' => $refreshResult['expires_at'],
                'updated_at' => date('Y-m-d H:i:s')
            ],
            'id = ?',
            [$accountId]
        );
        
        return [
            'message' => 'Token rafraîchi avec succès',
            'expires_at' => $refreshResult['expires_at']
        ];
    } else {
        throw new Exception('Échec du rafraîchissement du token : ' . $refreshResult['error']);
    }
}

/**
 * Teste la connexion à un compte social (simulation)
 */
function testSocialConnection($platform, $accessToken) {
    // Dans un vrai projet, vous feriez un appel API réel
    $testResults = [
        'facebook' => ['valid' => true, 'message' => 'Connexion Facebook OK'],
        'instagram' => ['valid' => true, 'message' => 'Connexion Instagram OK'],
        'twitter' => ['valid' => true, 'message' => 'Connexion Twitter OK'],
        'linkedin' => ['valid' => true, 'message' => 'Connexion LinkedIn OK'],
        'tiktok' => ['valid' => true, 'message' => 'Connexion TikTok OK'],
        'youtube' => ['valid' => true, 'message' => 'Connexion YouTube OK'],
        'pinterest' => ['valid' => true, 'message' => 'Connexion Pinterest OK'],
        'reddit' => ['valid' => true, 'message' => 'Connexion Reddit OK']
    ];
    
    return $testResults[$platform] ?? ['valid' => false, 'message' => 'Plateforme non supportée'];
}

/**
 * Rafraîchit un token d'accès (simulation)
 */
function refreshAccessToken($platform, $refreshToken) {
    // Simulation du rafraîchissement
    return [
        'success' => true,
        'access_token' => 'new_access_token_' . time(),
        'expires_at' => date('Y-m-d H:i:s', time() + 3600) // 1 heure
    ];
}
?>
