<?php
require_once '../config/database.php';
require_once '../config/auth.php';

/**
 * API de connexion des comptes de médias sociaux
 */

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
    exit();
}

try {
    $auth = new Auth();
    $permissions = new Permissions();
    $db = new DatabaseHelper();
    
    // Vérification de l'authentification
    $user = $auth->requireAuth();
    
    // Récupération des données JSON
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Données JSON invalides');
    }
    
    $action = $input['action'] ?? 'connect';
    
    switch ($action) {
        case 'connect':
            $result = connectSocialAccount($db, $permissions, $user, $input);
            break;
            
        case 'disconnect':
            $result = disconnectSocialAccount($db, $user, $input);
            break;
            
        case 'list':
            $result = listSocialAccounts($db, $user);
            break;
            
        case 'refresh':
            $result = refreshSocialAccount($db, $user, $input);
            break;
            
        default:
            throw new Exception('Action non supportée');
    }
    
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'data' => $result
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Connecte un nouveau compte de média social
 */
function connectSocialAccount($db, $permissions, $user, $input) {
    // Validation des champs requis
    $requiredFields = ['platform', 'account_name', 'account_id', 'access_token'];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            throw new Exception("Le champ '{$field}' est requis");
        }
    }
    
    $platform = $input['platform'];
    $accountName = $input['account_name'];
    $accountId = $input['account_id'];
    $accessToken = $input['access_token'];
    $refreshToken = $input['refresh_token'] ?? null;
    $tokenExpiresAt = $input['token_expires_at'] ?? null;
    $profilePicture = $input['profile_picture'] ?? null;
    $followersCount = $input['followers_count'] ?? 0;
    
    // Validation de la plateforme
    $validPlatforms = ['facebook', 'instagram', 'twitter', 'linkedin', 'tiktok', 'youtube', 'pinterest', 'reddit'];
    if (!in_array($platform, $validPlatforms)) {
        throw new Exception('Plateforme non supportée');
    }
    
    // Vérification des limites du plan
    if (!$permissions->checkPlanLimits($user['id'], 'social_accounts')) {
        throw new Exception('Limite de comptes sociaux atteinte pour votre plan');
    }
    
    // Vérification si le compte existe déjà
    $existingAccount = $db->fetchOne(
        "SELECT id FROM social_accounts WHERE user_id = ? AND platform = ? AND account_id = ?",
        [$user['id'], $platform, $accountId]
    );
    
    if ($existingAccount) {
        // Mise à jour du compte existant
        $db->update(
            'social_accounts',
            [
                'account_name' => $accountName,
                'access_token' => $accessToken,
                'refresh_token' => $refreshToken,
                'token_expires_at' => $tokenExpiresAt,
                'profile_picture' => $profilePicture,
                'followers_count' => $followersCount,
                'is_active' => 1,
                'updated_at' => date('Y-m-d H:i:s')
            ],
            'id = ?',
            [$existingAccount['id']]
        );
        
        $accountDbId = $existingAccount['id'];
        $message = 'Compte mis à jour avec succès';
    } else {
        // Création d'un nouveau compte
        $accountDbId = $db->insert('social_accounts', [
            'user_id' => $user['id'],
            'platform' => $platform,
            'account_name' => $accountName,
            'account_id' => $accountId,
            'access_token' => $accessToken,
            'refresh_token' => $refreshToken,
            'token_expires_at' => $tokenExpiresAt,
            'profile_picture' => $profilePicture,
            'followers_count' => $followersCount,
            'is_active' => 1
        ]);
        
        $message = 'Compte connecté avec succès';
    }
    
    // Récupération du compte créé/mis à jour
    $account = $db->fetchOne(
        "SELECT * FROM social_accounts WHERE id = ?",
        [$accountDbId]
    );
    
    // Test de la connexion
    $connectionTest = testSocialConnection($platform, $accessToken);
    
    return [
        'message' => $message,
        'account' => $account,
        'connection_test' => $connectionTest
    ];
}

/**
 * Déconnecte un compte de média social
 */
function disconnectSocialAccount($db, $user, $input) {
    $accountId = $input['account_id'] ?? null;
    
    if (!$accountId) {
        throw new Exception('ID du compte requis');
    }
    
    // Vérification que le compte appartient à l'utilisateur
    $account = $db->fetchOne(
        "SELECT * FROM social_accounts WHERE id = ? AND user_id = ?",
        [$accountId, $user['id']]
    );
    
    if (!$account) {
        throw new Exception('Compte non trouvé');
    }
    
    // Vérification s'il y a des publications planifiées
    $scheduledPosts = $db->fetchOne(
        "SELECT COUNT(*) as count FROM scheduled_posts 
         WHERE social_account_id = ? AND status = 'pending'",
        [$accountId]
    );
    
    if ($scheduledPosts['count'] > 0) {
        throw new Exception('Impossible de déconnecter : des publications sont planifiées sur ce compte');
    }
    
    // Désactivation du compte
    $db->update(
        'social_accounts',
        [
            'is_active' => 0,
            'access_token' => null,
            'refresh_token' => null,
            'updated_at' => date('Y-m-d H:i:s')
        ],
        'id = ?',
        [$accountId]
    );
    
    return [
        'message' => 'Compte déconnecté avec succès',
        'account_id' => $accountId
    ];
}

/**
 * Liste les comptes de médias sociaux de l'utilisateur
 */
function listSocialAccounts($db, $user) {
    $accounts = $db->fetchAll(
        "SELECT id, platform, account_name, account_id, profile_picture, 
                followers_count, is_active, token_expires_at, connected_at, updated_at
         FROM social_accounts 
         WHERE user_id = ? 
         ORDER BY platform, account_name",
        [$user['id']]
    );
    
    // Enrichissement avec les statistiques
    foreach ($accounts as &$account) {
        // Nombre de publications planifiées
        $scheduledCount = $db->fetchOne(
            "SELECT COUNT(*) as count FROM scheduled_posts 
             WHERE social_account_id = ? AND status = 'pending'",
            [$account['id']]
        );
        
        // Nombre de publications publiées
        $publishedCount = $db->fetchOne(
            "SELECT COUNT(*) as count FROM scheduled_posts 
             WHERE social_account_id = ? AND status = 'published'",
            [$account['id']]
        );
        
        $account['scheduled_posts'] = (int)$scheduledCount['count'];
        $account['published_posts'] = (int)$publishedCount['count'];
        
        // Vérification de l'expiration du token
        if ($account['token_expires_at']) {
            $account['token_expired'] = strtotime($account['token_expires_at']) <= time();
        } else {
            $account['token_expired'] = false;
        }
    }
    
    return [
        'accounts' => $accounts,
        'total_count' => count($accounts),
        'active_count' => count(array_filter($accounts, fn($a) => $a['is_active']))
    ];
}

/**
 * Rafraîchit un token d'accès
 */
function refreshSocialAccount($db, $user, $input) {
    $accountId = $input['account_id'] ?? null;
    
    if (!$accountId) {
        throw new Exception('ID du compte requis');
    }
    
    $account = $db->fetchOne(
        "SELECT * FROM social_accounts WHERE id = ? AND user_id = ?",
        [$accountId, $user['id']]
    );
    
    if (!$account) {
        throw new Exception('Compte non trouvé');
    }
    
    // Simulation du rafraîchissement du token
    $refreshResult = refreshAccessToken($account['platform'], $account['refresh_token']);
    
    if ($refreshResult['success']) {
        // Mise à jour du token
        $db->update(
            'social_accounts',
            [
                'access_token' => $refreshResult['access_token'],
                'token_expires_at' => $refreshResult['expires_at'],
                'updated_at' => date('Y-m-d H:i:s')
            ],
            'id = ?',
            [$accountId]
        );
        
        return [
            'message' => 'Token rafraîchi avec succès',
            'expires_at' => $refreshResult['expires_at']
        ];
    } else {
        throw new Exception('Échec du rafraîchissement du token : ' . $refreshResult['error']);
    }
}

/**
 * Teste la connexion à un compte social (simulation)
 */
function testSocialConnection($platform, $accessToken) {
    // Dans un vrai projet, vous feriez un appel API réel
    $testResults = [
        'facebook' => ['valid' => true, 'message' => 'Connexion Facebook OK'],
        'instagram' => ['valid' => true, 'message' => 'Connexion Instagram OK'],
        'twitter' => ['valid' => true, 'message' => 'Connexion Twitter OK'],
        'linkedin' => ['valid' => true, 'message' => 'Connexion LinkedIn OK'],
        'tiktok' => ['valid' => true, 'message' => 'Connexion TikTok OK'],
        'youtube' => ['valid' => true, 'message' => 'Connexion YouTube OK'],
        'pinterest' => ['valid' => true, 'message' => 'Connexion Pinterest OK'],
        'reddit' => ['valid' => true, 'message' => 'Connexion Reddit OK']
    ];
    
    return $testResults[$platform] ?? ['valid' => false, 'message' => 'Plateforme non supportée'];
}

/**
 * Rafraîchit un token d'accès (simulation)
 */
function refreshAccessToken($platform, $refreshToken) {
    // Simulation du rafraîchissement
    return [
        'success' => true,
        'access_token' => 'new_access_token_' . time(),
        'expires_at' => date('Y-m-d H:i:s', time() + 3600) // 1 heure
    ];
}
?>
