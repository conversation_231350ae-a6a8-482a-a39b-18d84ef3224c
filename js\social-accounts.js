/**
 * Gestion des comptes de médias sociaux
 */

class SocialAccountsManager {
    constructor() {
        this.accounts = [];
        this.currentAccount = null;
        this.init();
    }

    async init() {
        // Vérifier l'authentification
        if (!Utils.requireAuth()) {
            return;
        }

        try {
            // Charger les données utilisateur
            await this.loadUserData();
            
            // Charger les comptes connectés
            await this.loadAccounts();
            
            // Initialiser l'interface
            this.initUI();
            
        } catch (error) {
            console.error('Social accounts initialization error:', error);
            Utils.showAlert('Erreur lors du chargement des comptes sociaux', 'error');
        }
    }

    async loadUserData() {
        const userData = localStorage.getItem('user_data');
        if (userData) {
            this.user = JSON.parse(userData);
            this.updateUserInfo();
        }
    }

    updateUserInfo() {
        if (!this.user) return;

        const userNameEl = document.getElementById('user-name');
        const userPlanEl = document.getElementById('user-plan');
        const userAvatarEl = document.getElementById('user-avatar');

        if (userNameEl) {
            userNameEl.textContent = `${this.user.first_name} ${this.user.last_name}`;
        }

        if (userPlanEl) {
            userPlanEl.textContent = `Plan ${this.user.plan_type.charAt(0).toUpperCase() + this.user.plan_type.slice(1)}`;
        }

        if (userAvatarEl && this.user.avatar_url) {
            userAvatarEl.src = this.user.avatar_url;
        }
    }

    async loadAccounts() {
        try {
            const response = await Utils.apiRequest('social/connect.php', {
                method: 'POST',
                body: JSON.stringify({ action: 'list' })
            });
            if (response.success) {
                this.accounts = response.data.accounts;
                this.updateAccountsDisplay();
            }
        } catch (error) {
            console.error('Accounts loading error:', error);
            // Utiliser des données simulées en cas d'erreur
            this.accounts = this.generateSampleAccounts();
            this.updateAccountsDisplay();
        }
    }

    generateSampleAccounts() {
        return [
            {
                id: 1,
                platform: 'facebook',
                account_name: 'Ma Page Facebook',
                account_id: 'fb_123456789',
                profile_picture: 'https://images.unsplash.com/photo-*************-80b023f02d71?w=50&h=50&fit=crop&crop=face',
                followers_count: 1250,
                is_active: true,
                connected_at: '2024-01-15T10:30:00Z',
                last_sync: '2024-01-20T14:22:00Z'
            },
            {
                id: 2,
                platform: 'instagram',
                account_name: '@mon_instagram',
                account_id: 'ig_987654321',
                profile_picture: 'https://images.unsplash.com/photo-*************-80b023f02d71?w=50&h=50&fit=crop&crop=face',
                followers_count: 2340,
                is_active: true,
                connected_at: '2024-01-10T09:15:00Z',
                last_sync: '2024-01-20T14:20:00Z'
            },
            {
                id: 3,
                platform: 'twitter',
                account_name: '@mon_twitter',
                account_id: 'tw_456789123',
                profile_picture: 'https://images.unsplash.com/photo-*************-80b023f02d71?w=50&h=50&fit=crop&crop=face',
                followers_count: 890,
                is_active: false,
                connected_at: '2024-01-05T16:45:00Z',
                last_sync: '2024-01-18T11:30:00Z',
                error_message: 'Token expiré - Reconnexion requise'
            }
        ];
    }

    updateAccountsDisplay() {
        const container = document.getElementById('connected-accounts');
        if (!container) return;

        if (this.accounts.length === 0) {
            container.innerHTML = this.createEmptyState();
            return;
        }

        const accountsHTML = this.accounts.map(account => this.createAccountCard(account)).join('');
        container.innerHTML = accountsHTML;
    }

    createEmptyState() {
        return `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-link"></i>
                </div>
                <h3>Aucun compte connecté</h3>
                <p>Connectez vos comptes de réseaux sociaux pour commencer à planifier vos publications</p>
                <button class="btn btn-primary" onclick="showConnectModal()">
                    <i class="fas fa-plus"></i>
                    Connecter un compte
                </button>
            </div>
        `;
    }

    createAccountCard(account) {
        const platformInfo = this.getPlatformInfo(account.platform);
        const statusClass = account.is_active ? 'account-active' : 'account-inactive';
        const statusText = account.is_active ? 'Connecté' : 'Déconnecté';
        const lastSync = account.last_sync ? Utils.formatDate(account.last_sync) : 'Jamais';

        return `
            <div class="account-card ${statusClass}" data-account-id="${account.id}">
                <div class="account-header">
                    <div class="account-avatar">
                        <img src="${account.profile_picture || 'https://via.placeholder.com/50'}" alt="${account.account_name}">
                        <div class="platform-badge">
                            <i class="${platformInfo.icon}"></i>
                        </div>
                    </div>
                    <div class="account-info">
                        <h3 class="account-name">${Utils.escapeHtml(account.account_name)}</h3>
                        <p class="account-platform">${platformInfo.name}</p>
                        <div class="account-status">
                            <span class="status-indicator ${statusClass}"></span>
                            ${statusText}
                        </div>
                    </div>
                    <div class="account-actions">
                        <button class="btn-icon" onclick="viewAccount(${account.id})" title="Voir les détails">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-icon" onclick="refreshAccount(${account.id})" title="Actualiser">
                            <i class="fas fa-sync"></i>
                        </button>
                        <div class="dropdown">
                            <button class="btn-icon dropdown-toggle" onclick="toggleAccountMenu(${account.id})">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="dropdown-menu" id="account-menu-${account.id}">
                                <a href="#" class="dropdown-item" onclick="editAccount(${account.id})">
                                    <i class="fas fa-edit"></i>
                                    Modifier
                                </a>
                                <a href="#" class="dropdown-item" onclick="testConnection(${account.id})">
                                    <i class="fas fa-plug"></i>
                                    Tester la connexion
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="#" class="dropdown-item text-error" onclick="disconnectAccount(${account.id})">
                                    <i class="fas fa-unlink"></i>
                                    Déconnecter
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="account-stats">
                    <div class="stat">
                        <div class="stat-value">${Utils.formatNumber(account.followers_count || 0)}</div>
                        <div class="stat-label">Abonnés</div>
                    </div>
                    <div class="stat">
                        <div class="stat-value">${account.scheduled_posts || 0}</div>
                        <div class="stat-label">Planifiées</div>
                    </div>
                    <div class="stat">
                        <div class="stat-value">${account.published_posts || 0}</div>
                        <div class="stat-label">Publiées</div>
                    </div>
                </div>

                <div class="account-footer">
                    <div class="last-sync">
                        <i class="fas fa-clock"></i>
                        Dernière sync: ${lastSync}
                    </div>
                    ${account.error_message ? `
                        <div class="account-error">
                            <i class="fas fa-exclamation-triangle"></i>
                            ${Utils.escapeHtml(account.error_message)}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    getPlatformInfo(platform) {
        const platforms = {
            facebook: { name: 'Facebook', icon: 'fab fa-facebook-f', color: '#1877f2' },
            instagram: { name: 'Instagram', icon: 'fab fa-instagram', color: '#e4405f' },
            twitter: { name: 'Twitter', icon: 'fab fa-twitter', color: '#1da1f2' },
            linkedin: { name: 'LinkedIn', icon: 'fab fa-linkedin-in', color: '#0077b5' },
            tiktok: { name: 'TikTok', icon: 'fab fa-tiktok', color: '#000000' },
            youtube: { name: 'YouTube', icon: 'fab fa-youtube', color: '#ff0000' },
            pinterest: { name: 'Pinterest', icon: 'fab fa-pinterest-p', color: '#bd081c' },
            reddit: { name: 'Reddit', icon: 'fab fa-reddit-alien', color: '#ff4500' }
        };
        return platforms[platform] || { name: platform, icon: 'fas fa-share-alt', color: '#666' };
    }

    showConnectModal() {
        const modal = document.getElementById('connect-modal');
        const title = document.getElementById('connect-modal-title');
        const body = document.getElementById('connect-modal-body');
        
        if (modal && title && body) {
            title.textContent = 'Connecter un compte';
            body.innerHTML = this.createConnectContent();
            modal.style.display = 'block';
        }
    }

    createConnectContent() {
        return `
            <div class="connect-content">
                <div class="connect-info">
                    <h4>Choisissez une plateforme</h4>
                    <p>Sélectionnez la plateforme que vous souhaitez connecter à votre compte Social Scheduler.</p>
                </div>
                
                <div class="connect-platforms">
                    <button class="connect-platform-btn" onclick="connectPlatform('facebook')">
                        <div class="platform-icon facebook">
                            <i class="fab fa-facebook-f"></i>
                        </div>
                        <span>Facebook</span>
                    </button>
                    
                    <button class="connect-platform-btn" onclick="connectPlatform('instagram')">
                        <div class="platform-icon instagram">
                            <i class="fab fa-instagram"></i>
                        </div>
                        <span>Instagram</span>
                    </button>
                    
                    <button class="connect-platform-btn" onclick="connectPlatform('twitter')">
                        <div class="platform-icon twitter">
                            <i class="fab fa-twitter"></i>
                        </div>
                        <span>Twitter</span>
                    </button>
                    
                    <button class="connect-platform-btn" onclick="connectPlatform('linkedin')">
                        <div class="platform-icon linkedin">
                            <i class="fab fa-linkedin-in"></i>
                        </div>
                        <span>LinkedIn</span>
                    </button>
                    
                    <button class="connect-platform-btn" onclick="connectPlatform('tiktok')">
                        <div class="platform-icon tiktok">
                            <i class="fab fa-tiktok"></i>
                        </div>
                        <span>TikTok</span>
                    </button>
                    
                    <button class="connect-platform-btn" onclick="connectPlatform('youtube')">
                        <div class="platform-icon youtube">
                            <i class="fab fa-youtube"></i>
                        </div>
                        <span>YouTube</span>
                    </button>
                </div>
                
                <div class="connect-note">
                    <i class="fas fa-info-circle"></i>
                    <p>Vos données sont sécurisées et nous ne stockons que les informations nécessaires au fonctionnement de l'application.</p>
                </div>
            </div>
        `;
    }

    closeConnectModal() {
        const modal = document.getElementById('connect-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    async connectPlatform(platform) {
        try {
            // Simuler le processus de connexion OAuth
            Utils.showAlert(`Redirection vers ${platform} pour l'authentification...`, 'info');

            // Appel API pour connecter le compte
            const response = await Utils.apiRequest('social/connect.php', {
                method: 'POST',
                body: JSON.stringify({
                    action: 'connect',
                    platform: platform
                })
            });

            if (response.success) {
                Utils.showAlert(`Connexion à ${platform} réussie !`, 'success');
                this.closeConnectModal();

                // Recharger la liste des comptes
                await this.loadSocialAccounts();
            } else {
                throw new Error(response.message || 'Erreur lors de la connexion');
            }

        } catch (error) {
            console.error('Connection error:', error);
            Utils.showAlert(`Erreur lors de la connexion à ${platform}: ${error.message}`, 'error');
        }
    }

    viewAccount(accountId) {
        const account = this.accounts.find(acc => acc.id === accountId);
        if (!account) return;

        const modal = document.getElementById('account-modal');
        const body = document.getElementById('account-modal-body');
        
        if (modal && body) {
            body.innerHTML = this.createAccountDetails(account);
            modal.style.display = 'block';
            this.currentAccount = account;
        }
    }

    createAccountDetails(account) {
        const platformInfo = this.getPlatformInfo(account.platform);
        
        return `
            <div class="account-details">
                <div class="account-header-details">
                    <div class="account-avatar-large">
                        <img src="${account.profile_picture || 'https://via.placeholder.com/80'}" alt="${account.account_name}">
                        <div class="platform-badge-large">
                            <i class="${platformInfo.icon}"></i>
                        </div>
                    </div>
                    <div class="account-info-details">
                        <h3>${Utils.escapeHtml(account.account_name)}</h3>
                        <p class="platform-name">${platformInfo.name}</p>
                        <div class="account-status ${account.is_active ? 'active' : 'inactive'}">
                            <span class="status-indicator"></span>
                            ${account.is_active ? 'Connecté' : 'Déconnecté'}
                        </div>
                    </div>
                </div>
                
                <div class="account-stats-details">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">${Utils.formatNumber(account.followers_count || 0)}</div>
                            <div class="stat-label">Abonnés</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">${account.scheduled_posts || 0}</div>
                            <div class="stat-label">Planifiées</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">${account.published_posts || 0}</div>
                            <div class="stat-label">Publiées</div>
                        </div>
                    </div>
                </div>
                
                <div class="account-info-grid">
                    <div class="info-item">
                        <label>ID du compte:</label>
                        <span>${Utils.escapeHtml(account.account_id)}</span>
                    </div>
                    
                    <div class="info-item">
                        <label>Connecté le:</label>
                        <span>${Utils.formatDate(account.connected_at)}</span>
                    </div>
                    
                    <div class="info-item">
                        <label>Dernière synchronisation:</label>
                        <span>${account.last_sync ? Utils.formatDate(account.last_sync) : 'Jamais'}</span>
                    </div>
                    
                    ${account.error_message ? `
                        <div class="info-item error">
                            <label>Erreur:</label>
                            <span>${Utils.escapeHtml(account.error_message)}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    closeAccountModal() {
        const modal = document.getElementById('account-modal');
        if (modal) {
            modal.style.display = 'none';
        }
        this.currentAccount = null;
    }

    async refreshAccount(accountId) {
        try {
            Utils.showAlert('Actualisation du compte en cours...', 'info');

            const response = await Utils.apiRequest('social/connect.php', {
                method: 'POST',
                body: JSON.stringify({
                    action: 'refresh',
                    account_id: accountId
                })
            });

            if (response.success) {
                Utils.showAlert('Compte actualisé avec succès', 'success');
                // Recharger la liste des comptes
                await this.loadSocialAccounts();
            } else {
                throw new Error(response.message || 'Erreur lors de l\'actualisation');
            }

        } catch (error) {
            console.error('Refresh error:', error);
            Utils.showAlert('Erreur lors de l\'actualisation: ' + error.message, 'error');
        }
    }

    async disconnectAccount(accountId) {
        if (!confirm('Êtes-vous sûr de vouloir déconnecter ce compte ?')) {
            return;
        }

        try {
            const response = await Utils.apiRequest('social/connect.php', {
                method: 'POST',
                body: JSON.stringify({
                    action: 'disconnect',
                    account_id: accountId
                })
            });

            if (response.success) {
                Utils.showAlert('Compte déconnecté avec succès', 'success');
                this.closeAccountModal();
                // Recharger la liste des comptes
                await this.loadSocialAccounts();
            } else {
                throw new Error(response.message || 'Erreur lors de la déconnexion');
            }

        } catch (error) {
            console.error('Disconnect error:', error);
            Utils.showAlert('Erreur lors de la déconnexion: ' + error.message, 'error');
        }
    }

    toggleAccountMenu(accountId) {
        const menu = document.getElementById(`account-menu-${accountId}`);
        if (menu) {
            // Fermer tous les autres menus
            document.querySelectorAll('.dropdown-menu').forEach(m => {
                if (m !== menu) m.classList.remove('show');
            });
            
            menu.classList.toggle('show');
        }
    }

    initUI() {
        // Fermer les dropdowns en cliquant à l'extérieur
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });

        // Fermer les modals en cliquant à l'extérieur
        window.addEventListener('click', (e) => {
            const connectModal = document.getElementById('connect-modal');
            const accountModal = document.getElementById('account-modal');
            
            if (e.target === connectModal) {
                this.closeConnectModal();
            }
            
            if (e.target === accountModal) {
                this.closeAccountModal();
            }
        });
    }
}

// Fonctions globales
function showConnectModal() {
    if (window.socialAccountsManager) {
        window.socialAccountsManager.showConnectModal();
    }
}

function closeConnectModal() {
    if (window.socialAccountsManager) {
        window.socialAccountsManager.closeConnectModal();
    }
}

function connectPlatform(platform) {
    if (window.socialAccountsManager) {
        window.socialAccountsManager.connectPlatform(platform);
    }
}

function viewAccount(accountId) {
    if (window.socialAccountsManager) {
        window.socialAccountsManager.viewAccount(accountId);
    }
}

function closeAccountModal() {
    if (window.socialAccountsManager) {
        window.socialAccountsManager.closeAccountModal();
    }
}

function refreshAccount(accountId) {
    if (window.socialAccountsManager) {
        window.socialAccountsManager.refreshAccount(accountId || window.socialAccountsManager.currentAccount?.id);
    }
}

function disconnectAccount(accountId) {
    if (window.socialAccountsManager) {
        window.socialAccountsManager.disconnectAccount(accountId || window.socialAccountsManager.currentAccount?.id);
    }
}

function toggleAccountMenu(accountId) {
    if (window.socialAccountsManager) {
        window.socialAccountsManager.toggleAccountMenu(accountId);
    }
}

function editAccount(accountId) {
    Utils.showAlert(`Modification du compte ${accountId} en cours de développement`, 'info');
}

function testConnection(accountId) {
    Utils.showAlert('Test de connexion en cours...', 'info');
    setTimeout(() => {
        Utils.showAlert('Connexion testée avec succès', 'success');
    }, 1500);
}

function showCreatePostModal() {
    Utils.showAlert('Redirection vers la création de publication...', 'info');
    setTimeout(() => {
        window.location.href = 'posts.html';
    }, 1000);
}

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    window.socialAccountsManager = new SocialAccountsManager();
});
