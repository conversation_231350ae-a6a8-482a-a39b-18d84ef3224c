/**
 * Gestion des publications
 */

class PostsManager {
    constructor() {
        this.posts = [];
        this.socialAccounts = [];
        this.currentPage = 1;
        this.postsPerPage = 12;
        this.totalPosts = 0;
        this.filters = {
            status: '',
            platform: '',
            search: ''
        };
        this.sortBy = 'created_at';
        this.sortOrder = 'desc';
        this.viewMode = 'grid';
        this.selectedPosts = new Set();
        this.init();
    }

    async init() {
        // Vérifier l'authentification
        if (!Utils.requireAuth()) {
            return;
        }

        try {
            // Charger les données utilisateur
            await this.loadUserData();
            
            // Charger les comptes sociaux
            await this.loadSocialAccounts();
            
            // Charger les publications
            await this.loadPosts();
            
            // Initialiser l'interface
            this.initUI();
            
        } catch (error) {
            console.error('Posts initialization error:', error);
            Utils.showAlert('Erreur lors du chargement des publications', 'error');
        }
    }

    async loadUserData() {
        const userData = localStorage.getItem('user_data');
        if (userData) {
            this.user = JSON.parse(userData);
            this.updateUserInfo();
        }
    }

    updateUserInfo() {
        if (!this.user) return;

        const userNameEl = document.getElementById('user-name');
        const userPlanEl = document.getElementById('user-plan');
        const userAvatarEl = document.getElementById('user-avatar');

        if (userNameEl) {
            userNameEl.textContent = `${this.user.first_name} ${this.user.last_name}`;
        }

        if (userPlanEl) {
            userPlanEl.textContent = `Plan ${this.user.plan_type.charAt(0).toUpperCase() + this.user.plan_type.slice(1)}`;
        }

        if (userAvatarEl && this.user.avatar_url) {
            userAvatarEl.src = this.user.avatar_url;
        }
    }

    async loadSocialAccounts() {
        try {
            const response = await Utils.apiRequest('social/connect.php', {
                method: 'POST',
                body: JSON.stringify({ action: 'list' })
            });
            if (response.success) {
                this.socialAccounts = response.data.accounts;
                this.updatePlatformsSelector();
            }
        } catch (error) {
            console.error('Social accounts loading error:', error);
            this.socialAccounts = this.generateSampleAccounts();
            this.updatePlatformsSelector();
        }
    }

    generateSampleAccounts() {
        return [
            { id: 1, platform: 'facebook', account_name: 'Ma Page Facebook', is_active: true },
            { id: 2, platform: 'instagram', account_name: '@mon_instagram', is_active: true },
            { id: 3, platform: 'twitter', account_name: '@mon_twitter', is_active: true },
            { id: 4, platform: 'linkedin', account_name: 'Mon LinkedIn', is_active: false }
        ];
    }

    async loadPosts() {
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.postsPerPage,
                sort_by: this.sortBy,
                sort_order: this.sortOrder,
                ...this.filters
            });

            const response = await Utils.apiRequest(`posts/list.php?${params}`);
            if (response.success) {
                this.posts = response.data.posts;
                this.totalPosts = response.data.total;
                this.updatePostsDisplay();
                this.updateStats();
                this.updatePagination();
            }
        } catch (error) {
            console.error('Posts loading error:', error);
            // Utiliser des données simulées en cas d'erreur
            this.posts = this.generateSamplePosts();
            this.totalPosts = this.posts.length;
            this.updatePostsDisplay();
            this.updateStats();
        }
    }

    generateSamplePosts() {
        const statuses = ['draft', 'scheduled', 'published', 'failed'];
        const platforms = ['facebook', 'instagram', 'twitter', 'linkedin'];
        const posts = [];

        for (let i = 1; i <= 20; i++) {
            const status = statuses[Math.floor(Math.random() * statuses.length)];
            const platform = platforms[Math.floor(Math.random() * platforms.length)];
            const date = new Date();
            date.setDate(date.getDate() + Math.floor(Math.random() * 30) - 15);

            posts.push({
                id: i,
                title: `Publication ${i}`,
                content: `Contenu de la publication ${i}. Lorem ipsum dolor sit amet, consectetur adipiscing elit.`,
                status: status,
                scheduled_at: status === 'scheduled' ? date.toISOString() : null,
                created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
                hashtags: ['#marketing', '#socialmedia'],
                scheduled_accounts: [{ platform: platform, account_name: `Compte ${platform}` }]
            });
        }

        return posts;
    }

    updatePostsDisplay() {
        const container = document.getElementById('posts-list');
        if (!container) return;

        if (this.posts.length === 0) {
            container.innerHTML = this.createEmptyState();
            return;
        }

        const postsHTML = this.posts.map(post => this.createPostCard(post)).join('');
        container.innerHTML = postsHTML;
        container.className = `posts-list view-${this.viewMode}`;
    }

    createEmptyState() {
        return `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h3>Aucune publication trouvée</h3>
                <p>Créez votre première publication pour commencer</p>
                <button class="btn btn-primary" onclick="showCreatePostModal()">
                    <i class="fas fa-plus"></i>
                    Créer une publication
                </button>
            </div>
        `;
    }

    createPostCard(post) {
        const statusClass = this.getStatusClass(post.status);
        const statusText = this.getStatusText(post.status);
        const platforms = this.createPlatformIcons(post.scheduled_accounts || []);
        const date = post.scheduled_at || post.created_at;
        const isSelected = this.selectedPosts.has(post.id);

        return `
            <div class="post-card ${isSelected ? 'selected' : ''}" data-post-id="${post.id}">
                <div class="post-card-header">
                    <div class="post-select">
                        <input type="checkbox" class="post-checkbox" data-post-id="${post.id}" ${isSelected ? 'checked' : ''}>
                    </div>
                    <div class="post-status">
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </div>
                    <div class="post-actions">
                        <button class="btn-icon" onclick="editPost(${post.id})" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon" onclick="duplicatePost(${post.id})" title="Dupliquer">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button class="btn-icon text-error" onclick="deletePost(${post.id})" title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                <div class="post-card-content" onclick="viewPost(${post.id})">
                    <h4 class="post-title">${Utils.escapeHtml(post.title || 'Sans titre')}</h4>
                    <p class="post-excerpt">${Utils.truncateText(Utils.escapeHtml(post.content), 120)}</p>
                    
                    <div class="post-platforms">
                        ${platforms}
                    </div>
                    
                    ${post.hashtags && post.hashtags.length > 0 ? `
                        <div class="post-hashtags">
                            ${post.hashtags.map(tag => `<span class="hashtag">${tag}</span>`).join('')}
                        </div>
                    ` : ''}
                </div>
                
                <div class="post-card-footer">
                    <div class="post-date">
                        <i class="fas fa-clock"></i>
                        ${Utils.formatDate(date)}
                    </div>
                    <div class="post-stats">
                        ${post.analytics ? `
                            <span class="stat">
                                <i class="fas fa-heart"></i>
                                ${post.analytics.likes_count || 0}
                            </span>
                            <span class="stat">
                                <i class="fas fa-comment"></i>
                                ${post.analytics.comments_count || 0}
                            </span>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    createPlatformIcons(accounts) {
        if (!accounts || accounts.length === 0) {
            return '<span class="no-platforms">Aucune plateforme</span>';
        }

        const platformIcons = {
            facebook: 'fab fa-facebook',
            instagram: 'fab fa-instagram',
            twitter: 'fab fa-twitter',
            linkedin: 'fab fa-linkedin',
            tiktok: 'fab fa-tiktok',
            youtube: 'fab fa-youtube',
            pinterest: 'fab fa-pinterest',
            reddit: 'fab fa-reddit'
        };

        return accounts.map(account => 
            `<i class="${platformIcons[account.platform] || 'fas fa-share-alt'}" title="${account.account_name || account.platform}"></i>`
        ).join('');
    }

    getStatusClass(status) {
        const classes = {
            draft: 'status-draft',
            scheduled: 'status-scheduled',
            published: 'status-published',
            failed: 'status-failed'
        };
        return classes[status] || 'status-draft';
    }

    getStatusText(status) {
        const texts = {
            draft: 'Brouillon',
            scheduled: 'Planifié',
            published: 'Publié',
            failed: 'Échec'
        };
        return texts[status] || 'Inconnu';
    }

    updateStats() {
        const stats = this.calculateStats();
        
        const totalEl = document.getElementById('total-posts');
        const scheduledEl = document.getElementById('scheduled-posts');
        const publishedEl = document.getElementById('published-posts');
        const draftEl = document.getElementById('draft-posts');
        
        if (totalEl) totalEl.textContent = stats.total;
        if (scheduledEl) scheduledEl.textContent = stats.scheduled;
        if (publishedEl) publishedEl.textContent = stats.published;
        if (draftEl) draftEl.textContent = stats.draft;
    }

    calculateStats() {
        return {
            total: this.totalPosts,
            scheduled: this.posts.filter(p => p.status === 'scheduled').length,
            published: this.posts.filter(p => p.status === 'published').length,
            draft: this.posts.filter(p => p.status === 'draft').length
        };
    }

    updatePagination() {
        const container = document.getElementById('pagination-container');
        const pagination = document.getElementById('pagination');
        const info = document.getElementById('pagination-info');
        
        if (!container || !pagination || !info) return;

        const totalPages = Math.ceil(this.totalPosts / this.postsPerPage);
        
        if (totalPages <= 1) {
            container.style.display = 'none';
            return;
        }

        container.style.display = 'flex';
        
        // Info
        const start = (this.currentPage - 1) * this.postsPerPage + 1;
        const end = Math.min(this.currentPage * this.postsPerPage, this.totalPosts);
        info.textContent = `Affichage de ${start}-${end} sur ${this.totalPosts} publications`;
        
        // Pagination buttons
        let paginationHTML = '';
        
        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button class="pagination-btn" onclick="postsManager.goToPage(${this.currentPage - 1})">
                <i class="fas fa-chevron-left"></i>
            </button>`;
        }
        
        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);
        
        if (startPage > 1) {
            paginationHTML += `<button class="pagination-btn" onclick="postsManager.goToPage(1)">1</button>`;
            if (startPage > 2) {
                paginationHTML += `<span class="pagination-ellipsis">...</span>`;
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `<button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" onclick="postsManager.goToPage(${i})">${i}</button>`;
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<span class="pagination-ellipsis">...</span>`;
            }
            paginationHTML += `<button class="pagination-btn" onclick="postsManager.goToPage(${totalPages})">${totalPages}</button>`;
        }
        
        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="pagination-btn" onclick="postsManager.goToPage(${this.currentPage + 1})">
                <i class="fas fa-chevron-right"></i>
            </button>`;
        }
        
        pagination.innerHTML = paginationHTML;
    }

    updatePlatformsSelector() {
        const container = document.getElementById('platforms-selector');
        if (!container) return;

        const platformsHTML = this.socialAccounts.map(account => {
            const iconClass = this.getPlatformIcon(account.platform);
            return `
                <label class="platform-option ${account.is_active ? '' : 'disabled'}">
                    <input type="checkbox" name="platforms[]" value="${account.id}" ${account.is_active ? '' : 'disabled'}>
                    <span class="platform-icon">
                        <i class="${iconClass}"></i>
                    </span>
                    <span class="platform-name">${account.account_name}</span>
                    ${!account.is_active ? '<span class="platform-status">Déconnecté</span>' : ''}
                </label>
            `;
        }).join('');

        container.innerHTML = platformsHTML || '<p class="no-accounts">Aucun compte connecté. <a href="social-accounts.html">Connecter un compte</a></p>';
    }

    getPlatformIcon(platform) {
        const icons = {
            facebook: 'fab fa-facebook',
            instagram: 'fab fa-instagram',
            twitter: 'fab fa-twitter',
            linkedin: 'fab fa-linkedin',
            tiktok: 'fab fa-tiktok',
            youtube: 'fab fa-youtube',
            pinterest: 'fab fa-pinterest',
            reddit: 'fab fa-reddit'
        };
        return icons[platform] || 'fas fa-share-alt';
    }

    initUI() {
        // Gestionnaires d'événements pour les filtres
        const statusFilter = document.getElementById('status-filter');
        const platformFilter = document.getElementById('platform-filter');
        const searchInput = document.getElementById('search-input');
        
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.currentPage = 1;
                this.loadPosts();
            });
        }
        
        if (platformFilter) {
            platformFilter.addEventListener('change', (e) => {
                this.filters.platform = e.target.value;
                this.currentPage = 1;
                this.loadPosts();
            });
        }
        
        if (searchInput) {
            const debouncedSearch = Utils.debounce((value) => {
                this.filters.search = value;
                this.currentPage = 1;
                this.loadPosts();
            }, 500);
            
            searchInput.addEventListener('input', (e) => {
                debouncedSearch(e.target.value);
            });
        }

        // Gestionnaires pour les vues
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.target.dataset.view;
                if (view) {
                    this.viewMode = view;
                    document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                    this.updatePostsDisplay();
                }
            });
        });

        // Gestionnaire pour le tri
        const sortBy = document.getElementById('sort-by');
        const sortOrder = document.getElementById('sort-order');
        
        if (sortBy) {
            sortBy.addEventListener('change', (e) => {
                this.sortBy = e.target.value;
                this.loadPosts();
            });
        }
        
        if (sortOrder) {
            sortOrder.addEventListener('click', () => {
                this.sortOrder = this.sortOrder === 'desc' ? 'asc' : 'desc';
                sortOrder.dataset.order = this.sortOrder;
                sortOrder.innerHTML = `<i class="fas fa-sort-amount-${this.sortOrder === 'desc' ? 'down' : 'up'}"></i>`;
                this.loadPosts();
            });
        }

        // Gestionnaire pour la sélection multiple
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('post-checkbox')) {
                const postId = parseInt(e.target.dataset.postId);
                if (e.target.checked) {
                    this.selectedPosts.add(postId);
                } else {
                    this.selectedPosts.delete(postId);
                }
                this.updateBulkActions();
                this.updatePostSelection();
            }
        });

        // Gestionnaire pour le formulaire de publication
        const postStatus = document.getElementById('post-status');
        if (postStatus) {
            postStatus.addEventListener('change', (e) => {
                const scheduleGroup = document.getElementById('schedule-group');
                if (scheduleGroup) {
                    scheduleGroup.style.display = e.target.value === 'scheduled' ? 'block' : 'none';
                }
            });
        }

        // Compteur de caractères
        const postContent = document.getElementById('post-content');
        const characterCount = document.getElementById('character-count');
        if (postContent && characterCount) {
            postContent.addEventListener('input', (e) => {
                const count = e.target.value.length;
                characterCount.textContent = `${count}/280`;
                characterCount.className = count > 280 ? 'character-count over-limit' : 'character-count';
            });
        }
    }

    updateBulkActions() {
        const bulkActions = document.getElementById('bulk-actions');
        const selectedCount = document.getElementById('selected-count');
        
        if (bulkActions && selectedCount) {
            const count = this.selectedPosts.size;
            if (count > 0) {
                bulkActions.style.display = 'flex';
                selectedCount.textContent = `${count} sélectionnée${count > 1 ? 's' : ''}`;
            } else {
                bulkActions.style.display = 'none';
            }
        }
    }

    updatePostSelection() {
        document.querySelectorAll('.post-card').forEach(card => {
            const postId = parseInt(card.dataset.postId);
            if (this.selectedPosts.has(postId)) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }
        });
    }

    goToPage(page) {
        this.currentPage = page;
        this.loadPosts();
    }

    showCreatePostModal() {
        const modal = document.getElementById('post-modal');
        const form = document.getElementById('post-form');
        const title = document.getElementById('modal-title');
        
        if (modal && form && title) {
            form.reset();
            document.getElementById('post-id').value = '';
            title.textContent = 'Nouvelle publication';
            modal.style.display = 'block';
            
            // Focus sur le contenu
            setTimeout(() => {
                document.getElementById('post-content').focus();
            }, 100);
        }
    }

    closePostModal() {
        const modal = document.getElementById('post-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    async savePost() {
        const form = document.getElementById('post-form');
        const submitBtn = form.querySelector('button[onclick="savePost()"]');

        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Désactiver le bouton et afficher le loader
        this.setButtonLoading(submitBtn, true);

        try {
            const formData = new FormData(form);
            const postData = {
                title: formData.get('title'),
                content: formData.get('content'),
                status: formData.get('status'),
                scheduled_at: formData.get('scheduled_at'),
                hashtags: formData.get('hashtags'),
                platforms: Array.from(form.querySelectorAll('input[name="platforms[]"]:checked')).map(cb => cb.value)
            };

            const postId = formData.get('post_id');
            const endpoint = postId ? 'posts/update.php' : 'posts/create_simple.php';
            if (postId) postData.id = postId;

            const response = await Utils.apiRequest(endpoint, {
                method: 'POST',
                body: JSON.stringify(postData)
            });

            if (response.success) {
                Utils.showAlert(postId ? 'Publication mise à jour' : 'Publication créée avec succès', 'success');
                this.closePostModal();
                this.loadPosts();
            } else {
                throw new Error(response.message || 'Erreur lors de la sauvegarde');
            }
        } catch (error) {
            console.error('Save post error:', error);
            Utils.showAlert(error.message || 'Erreur lors de la sauvegarde', 'error');
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }

    setButtonLoading(button, loading) {
        const btnText = button.querySelector('.btn-text');
        const btnLoader = button.querySelector('.btn-loader');
        
        if (loading) {
            button.disabled = true;
            btnText.style.display = 'none';
            btnLoader.style.display = 'block';
        } else {
            button.disabled = false;
            btnText.style.display = 'block';
            btnLoader.style.display = 'none';
        }
    }
}

// Fonctions globales
function showCreatePostModal() {
    if (window.postsManager) {
        window.postsManager.showCreatePostModal();
    }
}

function closePostModal() {
    if (window.postsManager) {
        window.postsManager.closePostModal();
    }
}

function savePost() {
    if (window.postsManager) {
        window.postsManager.savePost();
    }
}

function editPost(postId) {
    Utils.showAlert(`Modification de la publication ${postId} en cours de développement`, 'info');
}

function duplicatePost(postId) {
    Utils.showAlert(`Duplication de la publication ${postId} en cours de développement`, 'info');
}

function deletePost(postId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette publication ?')) {
        Utils.showAlert(`Suppression de la publication ${postId} en cours de développement`, 'info');
    }
}

function viewPost(postId) {
    Utils.showAlert(`Affichage de la publication ${postId} en cours de développement`, 'info');
}

function bulkEdit() {
    Utils.showAlert('Modification en lot en cours de développement', 'info');
}

function bulkSchedule() {
    Utils.showAlert('Planification en lot en cours de développement', 'info');
}

function bulkDelete() {
    if (confirm('Êtes-vous sûr de vouloir supprimer les publications sélectionnées ?')) {
        Utils.showAlert('Suppression en lot en cours de développement', 'info');
    }
}

function showAIAssistant() {
    Utils.showAlert('Assistant IA en cours de développement', 'info');
}

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    window.postsManager = new PostsManager();
});

// Fermeture du modal en cliquant à l'extérieur
window.addEventListener('click', (e) => {
    const modal = document.getElementById('post-modal');
    if (e.target === modal) {
        closePostModal();
    }
});
