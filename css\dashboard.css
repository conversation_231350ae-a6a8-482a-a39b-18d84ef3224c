/* Dashboard Layout */
.dashboard-page {
    display: flex;
    min-height: 100vh;
    background-color: var(--gray-50);
}

/* Sidebar */
.sidebar {
    width: 280px;
    background-color: var(--white);
    border-right: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: var(--z-fixed);
    transition: transform var(--transition-normal);
}

.sidebar.collapsed {
    transform: translateX(-100%);
}

.sidebar-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
}

.sidebar-toggle:hover {
    background-color: var(--gray-100);
}

.sidebar-nav {
    flex: 1;
    padding: var(--spacing-4) 0;
    overflow-y: auto;
}

.sidebar-nav .nav-list {
    list-style: none;
    padding: 0 var(--spacing-4);
}

.sidebar-nav .nav-item {
    margin-bottom: var(--spacing-1);
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--gray-600);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
    font-weight: 500;
}

.sidebar-nav .nav-link:hover {
    background-color: var(--gray-100);
    color: var(--gray-900);
}

.sidebar-nav .nav-link.active {
    background-color: var(--primary-color);
    color: var(--white);
}

.sidebar-nav .nav-link i {
    width: 1.25rem;
    text-align: center;
}

.sidebar-footer {
    padding: var(--spacing-4);
    border-top: 1px solid var(--gray-200);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    position: relative;
}

.user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--border-radius-full);
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-plan {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.user-menu-btn {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-1);
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
}

.user-menu-btn:hover {
    background-color: var(--gray-100);
    color: var(--gray-600);
}

.user-menu-dropdown {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-2);
    margin-bottom: var(--spacing-2);
    display: none;
    z-index: var(--z-dropdown);
}

.user-menu-dropdown.show {
    display: block;
}

.user-menu-dropdown .menu-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-2) var(--spacing-3);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    transition: background-color var(--transition-fast);
}

.user-menu-dropdown .menu-item:hover {
    background-color: var(--gray-100);
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.dashboard-header {
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-4) var(--spacing-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
}

.mobile-menu-btn:hover {
    background-color: var(--gray-100);
}

.page-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.notifications {
    position: relative;
}

.notification-btn {
    background: none;
    border: none;
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
    position: relative;
    transition: background-color var(--transition-fast);
}

.notification-btn:hover {
    background-color: var(--gray-100);
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--error-color);
    color: var(--white);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: var(--border-radius-full);
    min-width: 1.25rem;
    text-align: center;
    line-height: 1;
}

.notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 320px;
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    margin-top: var(--spacing-2);
    display: none;
    z-index: var(--z-dropdown);
}

.notification-dropdown.show {
    display: block;
}

.notification-header {
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.notification-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
}

.mark-all-read {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--gray-100);
    transition: background-color var(--transition-fast);
}

.notification-item:hover {
    background-color: var(--gray-50);
}

.notification-item.unread {
    background-color: var(--primary-color);
    background-color: rgba(59, 130, 246, 0.05);
}

.notification-icon {
    flex-shrink: 0;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-full);
    background-color: var(--gray-100);
}

.notification-content {
    flex: 1;
}

.notification-content p {
    font-size: var(--font-size-sm);
    color: var(--gray-900);
    margin: 0 0 var(--spacing-1) 0;
}

.notification-time {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    padding: var(--spacing-6);
    overflow-y: auto;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.stat-card {
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    transition: all var(--transition-fast);
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    line-height: 1;
    margin-bottom: var(--spacing-1);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--spacing-2);
}

.stat-change {
    font-size: var(--font-size-xs);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--error-color);
}

/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.chart-card {
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-6);
}

.chart-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.chart-filters {
    display: flex;
    gap: var(--spacing-2);
}

.chart-filter {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    background-color: var(--white);
    font-size: var(--font-size-sm);
}

.chart-container {
    position: relative;
    height: 300px;
}

/* Responsive */
@media (max-width: 1024px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
}

/* Posts Page Styles */
.posts-content {
    padding: var(--spacing-6);
}

.posts-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-8);
}

.posts-stats .stat-item {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-4);
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.posts-stats .stat-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
}

.posts-filters {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.filter-select {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: var(--spacing-3);
    color: var(--gray-400);
}

.search-box input {
    padding: var(--spacing-2) var(--spacing-3) var(--spacing-2) var(--spacing-10);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    width: 250px;
}

.posts-list-container {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.posts-list-header {
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.list-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-4);
}

.view-toggle {
    display: flex;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.view-btn {
    padding: var(--spacing-2) var(--spacing-3);
    background: white;
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.sort-select {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
}

.sort-order-btn {
    padding: var(--spacing-2);
    background: white;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    cursor: pointer;
    color: var(--gray-600);
}

.bulk-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
}

.posts-list {
    padding: var(--spacing-6);
    min-height: 400px;
}

.posts-list.view-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-6);
}

.posts-list.view-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.post-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.post-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.post-card.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.post-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--gray-100);
}

.post-select input {
    margin: 0;
}

.status-badge {
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.status-badge.status-draft {
    background: var(--gray-100);
    color: var(--gray-700);
}

.status-badge.status-scheduled {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.status-published {
    background: #d1fae5;
    color: #065f46;
}

.status-badge.status-failed {
    background: #fee2e2;
    color: #991b1b;
}

.post-actions {
    display: flex;
    gap: var(--spacing-1);
}

.post-card-content {
    padding: var(--spacing-4);
}

.post-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
    color: var(--gray-900);
}

.post-excerpt {
    color: var(--gray-600);
    line-height: 1.5;
    margin-bottom: var(--spacing-3);
}

.post-platforms {
    display: flex;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-3);
}

.post-platforms i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.post-hashtags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-1);
}

.hashtag {
    background: var(--gray-100);
    color: var(--gray-700);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
}

.post-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4);
    border-top: 1px solid var(--gray-100);
    background: var(--gray-50);
}

.post-date {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.post-stats {
    display: flex;
    gap: var(--spacing-3);
}

.post-stats .stat {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.pagination-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.pagination {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.pagination-btn {
    padding: var(--spacing-2) var(--spacing-3);
    background: white;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
}

.pagination-btn:hover {
    background: var(--gray-50);
}

.pagination-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-ellipsis {
    padding: var(--spacing-2) var(--spacing-1);
    color: var(--gray-400);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: var(--z-modal);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-4);
}

.modal-content {
    background: white;
    border-radius: var(--border-radius-lg);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-xl);
}

.modal-large {
    max-width: 800px;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
}

.modal-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
    color: var(--gray-400);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.modal-body {
    padding: var(--spacing-6);
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-3);
    padding: var(--spacing-6);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

/* Post Form Styles */
.post-form .form-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-4);
}

.content-tools {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: var(--spacing-2);
}

.character-count {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.character-count.over-limit {
    color: var(--error-color);
}

.platforms-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-3);
}

.platform-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.platform-option:hover {
    border-color: var(--primary-color);
    background: var(--gray-50);
}

.platform-option.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.platform-option input {
    margin: 0;
}

.platform-icon {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
}

.platform-name {
    font-weight: 500;
}

.platform-status {
    font-size: var(--font-size-xs);
    color: var(--error-color);
}

.no-accounts {
    text-align: center;
    color: var(--gray-500);
    padding: var(--spacing-8);
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-menu-btn {
        display: block;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-content {
        padding: var(--spacing-4);
    }

    .notification-dropdown {
        width: 280px;
    }

    .posts-filters {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-2);
    }

    .search-box input {
        width: 100%;
    }

    .list-controls {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-3);
    }

    .posts-list.view-grid {
        grid-template-columns: 1fr;
    }

    .post-form .form-row {
        grid-template-columns: 1fr;
    }

    .platforms-selector {
        grid-template-columns: 1fr;
    }
}

/* Social Accounts Page Styles */
.social-accounts-content {
    padding: var(--spacing-6);
}

.accounts-section,
.platforms-section,
.help-section {
    margin-bottom: var(--spacing-12);
}

.section-header {
    margin-bottom: var(--spacing-6);
}

.section-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-2);
    color: var(--gray-900);
}

.section-header p {
    color: var(--gray-600);
    margin: 0;
}

.connected-accounts {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-6);
}

.account-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    transition: all var(--transition-fast);
    position: relative;
}

.account-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.account-card.account-active {
    border-left: 4px solid var(--success-color);
}

.account-card.account-inactive {
    border-left: 4px solid var(--error-color);
}

.account-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-4);
}

.account-avatar {
    position: relative;
    flex-shrink: 0;
}

.account-avatar img {
    width: 3rem;
    height: 3rem;
    border-radius: var(--border-radius-full);
    object-fit: cover;
}

.platform-badge {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 1.25rem;
    height: 1.25rem;
    background: white;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
    color: var(--primary-color);
    border: 2px solid white;
}

.account-info {
    flex: 1;
    min-width: 0;
}

.account-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-1);
    color: var(--gray-900);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.account-platform {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    margin-bottom: var(--spacing-2);
}

.account-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.status-indicator {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: var(--border-radius-full);
}

.status-indicator.account-active {
    background: var(--success-color);
}

.status-indicator.account-inactive {
    background: var(--error-color);
}

.account-actions {
    display: flex;
    gap: var(--spacing-1);
    position: relative;
}

.dropdown {
    position: relative;
}

.dropdown-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
    color: var(--gray-400);
}

.dropdown-toggle:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-2);
    min-width: 150px;
    z-index: var(--z-dropdown);
    display: none;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    transition: background-color var(--transition-fast);
}

.dropdown-item:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.dropdown-item.text-error {
    color: var(--error-color);
}

.dropdown-divider {
    height: 1px;
    background: var(--gray-200);
    margin: var(--spacing-2) 0;
}

.account-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-4);
}

.account-stats .stat {
    text-align: center;
}

.account-stats .stat-value {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--gray-900);
}

.account-stats .stat-label {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    margin-top: var(--spacing-1);
}

.account-footer {
    border-top: 1px solid var(--gray-100);
    padding-top: var(--spacing-4);
}

.last-sync {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    margin-bottom: var(--spacing-2);
}

.account-error {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    color: var(--error-color);
    background: #fee2e2;
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius);
}

.available-platforms {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-6);
}

.platform-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    transition: all var(--transition-fast);
}

.platform-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.platform-card .platform-icon {
    width: 4rem;
    height: 4rem;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: white;
    margin-bottom: var(--spacing-4);
}

.platform-icon.facebook { background: #1877f2; }
.platform-icon.instagram { background: #e4405f; }
.platform-icon.twitter { background: #1da1f2; }
.platform-icon.linkedin { background: #0077b5; }
.platform-icon.tiktok { background: #000000; }
.platform-icon.youtube { background: #ff0000; }
.platform-icon.pinterest { background: #bd081c; }
.platform-icon.reddit { background: #ff4500; }

.platform-info h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
    color: var(--gray-900);
}

.platform-info p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-4);
}

.platform-features {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2);
    justify-content: center;
    margin-bottom: var(--spacing-6);
}

.feature {
    background: var(--gray-100);
    color: var(--gray-700);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
}

.help-section {
    display: flex;
    justify-content: center;
}

.help-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-8);
    display: flex;
    align-items: center;
    gap: var(--spacing-6);
    max-width: 600px;
}

.help-icon {
    width: 3rem;
    height: 3rem;
    background: var(--info-color);
    color: white;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.help-content h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
    color: var(--gray-900);
}

.help-content p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-4);
}

/* Connect Modal Styles */
.connect-content {
    text-align: center;
}

.connect-info {
    margin-bottom: var(--spacing-6);
}

.connect-info h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
    color: var(--gray-900);
}

.connect-platforms {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
}

.connect-platform-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-4);
    background: white;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.connect-platform-btn:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.connect-platform-btn .platform-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: white;
}

.connect-note {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
    background: var(--gray-50);
    border-radius: var(--border-radius);
    text-align: left;
}

.connect-note i {
    color: var(--info-color);
    margin-top: 0.125rem;
    flex-shrink: 0;
}

.connect-note p {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

/* Account Details Modal */
.account-details {
    text-align: center;
}

.account-header-details {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: var(--spacing-6);
}

.account-avatar-large {
    position: relative;
    margin-bottom: var(--spacing-4);
}

.account-avatar-large img {
    width: 5rem;
    height: 5rem;
    border-radius: var(--border-radius-full);
    object-fit: cover;
}

.platform-badge-large {
    position: absolute;
    bottom: -4px;
    right: -4px;
    width: 2rem;
    height: 2rem;
    background: white;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    border: 3px solid white;
    box-shadow: var(--shadow-sm);
}

.account-info-details h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-1);
    color: var(--gray-900);
}

.platform-name {
    font-size: var(--font-size-lg);
    color: var(--gray-500);
    margin-bottom: var(--spacing-3);
}

.account-stats-details {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
}

.stat-card {
    background: var(--gray-50);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-4);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2);
}

.stat-card .stat-icon {
    width: 2rem;
    height: 2rem;
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-card .stat-number {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
}

.stat-card .stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.account-info-grid {
    display: grid;
    gap: var(--spacing-3);
    text-align: left;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3);
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.info-item label {
    font-weight: 500;
    color: var(--gray-700);
}

.info-item span {
    color: var(--gray-900);
}

.info-item.error {
    background: #fee2e2;
}

.info-item.error label,
.info-item.error span {
    color: var(--error-color);
}

/* Analytics Page Styles */
.analytics-content {
    padding: var(--spacing-6);
}

.analytics-overview {
    margin-bottom: var(--spacing-8);
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
}

.overview-stats .stat-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    transition: all var(--transition-fast);
}

.overview-stats .stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.overview-stats .stat-icon {
    width: 3rem;
    height: 3rem;
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.overview-stats .stat-content {
    flex: 1;
}

.overview-stats .stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.overview-stats .stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--spacing-2);
}

.stat-change {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--error-color);
}

.analytics-filters {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.chart-container {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-6);
}

.chart-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.chart-select {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
}

.chart-wrapper {
    height: 300px;
    position: relative;
}

.performance-section,
.insights-section {
    margin-bottom: var(--spacing-8);
}

.performance-tabs {
    display: flex;
    border-bottom: 1px solid var(--gray-200);
    margin-bottom: var(--spacing-6);
}

.tab-btn {
    padding: var(--spacing-3) var(--spacing-6);
    background: none;
    border: none;
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-600);
    border-bottom: 2px solid transparent;
    transition: all var(--transition-fast);
}

.tab-btn:hover {
    color: var(--gray-900);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.posts-performance {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.post-performance-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    display: flex;
    align-items: center;
    gap: var(--spacing-6);
    transition: all var(--transition-fast);
}

.post-performance-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.post-rank {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.post-info {
    flex: 1;
    min-width: 0;
}

.post-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-2);
}

.post-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.post-platform {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    flex-shrink: 0;
}

.post-content {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    line-height: 1.4;
    margin-bottom: var(--spacing-2);
}

.post-date {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-xs);
    color: var(--gray-400);
}

.post-metrics {
    display: flex;
    gap: var(--spacing-6);
    flex-shrink: 0;
}

.metric {
    text-align: center;
}

.metric-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.metric-label {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.metric.highlight .metric-value {
    color: var(--primary-color);
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-6);
}

.insight-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    transition: all var(--transition-fast);
}

.insight-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.insight-icon {
    width: 3rem;
    height: 3rem;
    background: var(--info-color);
    color: white;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-4);
}

.insight-content h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.insight-content p {
    color: var(--gray-600);
    line-height: 1.5;
    margin-bottom: var(--spacing-4);
}

.insight-action {
    display: flex;
    justify-content: flex-start;
}

.no-data {
    text-align: center;
    color: var(--gray-500);
    padding: var(--spacing-8);
    font-style: italic;
}

@media (max-width: 768px) {
    .connected-accounts,
    .available-platforms {
        grid-template-columns: 1fr;
    }

    .help-card {
        flex-direction: column;
        text-align: center;
    }

    .connect-platforms {
        grid-template-columns: repeat(2, 1fr);
    }

    .account-stats-details {
        grid-template-columns: 1fr;
    }

    .analytics-filters {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-2);
    }

    .overview-stats {
        grid-template-columns: 1fr;
    }

    .charts-section {
        grid-template-columns: 1fr;
    }

    .performance-tabs {
        overflow-x: auto;
        white-space: nowrap;
    }

    .post-performance-card {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .post-header {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-2);
    }

    .post-metrics {
        justify-content: center;
        gap: var(--spacing-4);
    }

    .insights-grid {
        grid-template-columns: 1fr;
    }
}
