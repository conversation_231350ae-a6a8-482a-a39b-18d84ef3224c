/* Dashboard Layout */
.dashboard-page {
    display: flex;
    min-height: 100vh;
    background-color: var(--gray-50);
}

/* Sidebar */
.sidebar {
    width: 280px;
    background-color: var(--white);
    border-right: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: var(--z-fixed);
    transition: transform var(--transition-normal);
}

.sidebar.collapsed {
    transform: translateX(-100%);
}

.sidebar-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
}

.sidebar-toggle:hover {
    background-color: var(--gray-100);
}

.sidebar-nav {
    flex: 1;
    padding: var(--spacing-4) 0;
    overflow-y: auto;
}

.sidebar-nav .nav-list {
    list-style: none;
    padding: 0 var(--spacing-4);
}

.sidebar-nav .nav-item {
    margin-bottom: var(--spacing-1);
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--gray-600);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
    font-weight: 500;
}

.sidebar-nav .nav-link:hover {
    background-color: var(--gray-100);
    color: var(--gray-900);
}

.sidebar-nav .nav-link.active {
    background-color: var(--primary-color);
    color: var(--white);
}

.sidebar-nav .nav-link i {
    width: 1.25rem;
    text-align: center;
}

.sidebar-footer {
    padding: var(--spacing-4);
    border-top: 1px solid var(--gray-200);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    position: relative;
}

.user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--border-radius-full);
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-plan {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.user-menu-btn {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-1);
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
}

.user-menu-btn:hover {
    background-color: var(--gray-100);
    color: var(--gray-600);
}

.user-menu-dropdown {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-2);
    margin-bottom: var(--spacing-2);
    display: none;
    z-index: var(--z-dropdown);
}

.user-menu-dropdown.show {
    display: block;
}

.user-menu-dropdown .menu-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-2) var(--spacing-3);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    transition: background-color var(--transition-fast);
}

.user-menu-dropdown .menu-item:hover {
    background-color: var(--gray-100);
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.dashboard-header {
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-4) var(--spacing-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
}

.mobile-menu-btn:hover {
    background-color: var(--gray-100);
}

.page-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.notifications {
    position: relative;
}

.notification-btn {
    background: none;
    border: none;
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
    position: relative;
    transition: background-color var(--transition-fast);
}

.notification-btn:hover {
    background-color: var(--gray-100);
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--error-color);
    color: var(--white);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: var(--border-radius-full);
    min-width: 1.25rem;
    text-align: center;
    line-height: 1;
}

.notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 320px;
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    margin-top: var(--spacing-2);
    display: none;
    z-index: var(--z-dropdown);
}

.notification-dropdown.show {
    display: block;
}

.notification-header {
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.notification-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
}

.mark-all-read {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--gray-100);
    transition: background-color var(--transition-fast);
}

.notification-item:hover {
    background-color: var(--gray-50);
}

.notification-item.unread {
    background-color: var(--primary-color);
    background-color: rgba(59, 130, 246, 0.05);
}

.notification-icon {
    flex-shrink: 0;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-full);
    background-color: var(--gray-100);
}

.notification-content {
    flex: 1;
}

.notification-content p {
    font-size: var(--font-size-sm);
    color: var(--gray-900);
    margin: 0 0 var(--spacing-1) 0;
}

.notification-time {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    padding: var(--spacing-6);
    overflow-y: auto;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.stat-card {
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    transition: all var(--transition-fast);
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    line-height: 1;
    margin-bottom: var(--spacing-1);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--spacing-2);
}

.stat-change {
    font-size: var(--font-size-xs);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--error-color);
}

/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.chart-card {
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-6);
}

.chart-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.chart-filters {
    display: flex;
    gap: var(--spacing-2);
}

.chart-filter {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    background-color: var(--white);
    font-size: var(--font-size-sm);
}

.chart-container {
    position: relative;
    height: 300px;
}

/* Responsive */
@media (max-width: 1024px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .mobile-menu-btn {
        display: block;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-content {
        padding: var(--spacing-4);
    }
    
    .notification-dropdown {
        width: 280px;
    }
}
