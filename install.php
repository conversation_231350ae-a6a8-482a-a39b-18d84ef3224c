<?php
/**
 * Script d'installation automatique pour Social Scheduler
 */

// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Vérifier si l'installation a déjà été effectuée
if (file_exists('.installed')) {
    die('<h1>Installation déjà effectuée</h1><p>Supprimez le fichier .installed pour réinstaller.</p><p><a href="index.html">→ Aller à l\'application</a></p>');
}

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step == 2) {
        // Étape 2: Configuration de la base de données
        $host = $_POST['host'] ?? 'localhost';
        $username = $_POST['username'] ?? 'root';
        $password = $_POST['password'] ?? '';
        $dbname = $_POST['dbname'] ?? 'social_scheduler';
        
        try {
            // Test de connexion
            $dsn = "mysql:host=$host;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            // Créer la base de données si elle n'existe pas
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // Mettre à jour le fichier de configuration
            $config_content = file_get_contents('api/config/database.php');
            $config_content = str_replace("private \$host = 'localhost';", "private \$host = '$host';", $config_content);
            $config_content = str_replace("private \$db_name = 'social_scheduler';", "private \$db_name = '$dbname';", $config_content);
            $config_content = str_replace("private \$username = 'root';", "private \$username = '$username';", $config_content);
            $config_content = str_replace("private \$password = '';", "private \$password = '$password';", $config_content);
            
            file_put_contents('api/config/database.php', $config_content);
            
            $success = "Configuration de la base de données mise à jour avec succès !";
            $step = 3;
            
        } catch (Exception $e) {
            $error = "Erreur de connexion à la base de données: " . $e->getMessage();
        }
    } elseif ($step == 3) {
        // Étape 3: Import du schéma
        try {
            require_once 'api/config/database.php';
            $db = new Database();
            $conn = $db->getConnection();
            
            // Lire et exécuter le schéma SQL
            $schema = file_get_contents('database/schema.sql');
            
            // Diviser en requêtes individuelles
            $queries = array_filter(array_map('trim', explode(';', $schema)));
            
            foreach ($queries as $query) {
                if (!empty($query) && !preg_match('/^(--|\/\*|CREATE DATABASE|USE)/', $query)) {
                    $conn->exec($query);
                }
            }
            
            $success = "Schéma de base de données importé avec succès !";
            $step = 4;
            
        } catch (Exception $e) {
            $error = "Erreur lors de l'import du schéma: " . $e->getMessage();
        }
    } elseif ($step == 4) {
        // Étape 4: Finalisation
        try {
            // Créer le fichier .installed
            file_put_contents('.installed', date('Y-m-d H:i:s'));
            
            // Créer le dossier logs s'il n'existe pas
            if (!is_dir('logs')) {
                mkdir('logs', 0755, true);
            }
            
            $success = "Installation terminée avec succès !";
            $step = 5;
            
        } catch (Exception $e) {
            $error = "Erreur lors de la finalisation: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation - Social Scheduler</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #333; text-align: center; }
        .step { background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 20px 0; }
        .error { background: #ffebee; color: #c62828; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #e8f5e8; color: #2e7d32; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #2196f3;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover { background: #1976d2; }
        .progress {
            background: #e0e0e0;
            height: 10px;
            border-radius: 5px;
            margin: 20px 0;
            overflow: hidden;
        }
        .progress-bar {
            background: #2196f3;
            height: 100%;
            transition: width 0.3s;
        }
        .requirements { background: #fff3e0; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .check { color: #4caf50; }
        .cross { color: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Installation Social Scheduler</h1>
        
        <div class="progress">
            <div class="progress-bar" style="width: <?= ($step / 5) * 100 ?>%"></div>
        </div>
        
        <?php if ($error): ?>
            <div class="error"><?= htmlspecialchars($error) ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="success"><?= htmlspecialchars($success) ?></div>
        <?php endif; ?>
        
        <?php if ($step == 1): ?>
            <div class="step">Étape 1/4: Vérification des prérequis</div>
            
            <div class="requirements">
                <h3>Vérification de l'environnement:</h3>
                
                <p>
                    <?= version_compare(phpversion(), '7.4.0', '>=') ? '<span class="check">✓</span>' : '<span class="cross">✗</span>' ?>
                    Version PHP: <?= phpversion() ?> (minimum 7.4)
                </p>
                
                <p>
                    <?= extension_loaded('pdo') ? '<span class="check">✓</span>' : '<span class="cross">✗</span>' ?>
                    Extension PDO
                </p>
                
                <p>
                    <?= extension_loaded('pdo_mysql') ? '<span class="check">✓</span>' : '<span class="cross">✗</span>' ?>
                    Extension PDO MySQL
                </p>
                
                <p>
                    <?= extension_loaded('json') ? '<span class="check">✓</span>' : '<span class="cross">✗</span>' ?>
                    Extension JSON
                </p>
                
                <p>
                    <?= is_writable('.') ? '<span class="check">✓</span>' : '<span class="cross">✗</span>' ?>
                    Permissions d'écriture
                </p>
            </div>
            
            <?php if (version_compare(phpversion(), '7.4.0', '>=') && extension_loaded('pdo') && extension_loaded('pdo_mysql')): ?>
                <form method="get">
                    <input type="hidden" name="step" value="2">
                    <button type="submit">Continuer →</button>
                </form>
            <?php else: ?>
                <p style="color: red;">Veuillez corriger les erreurs ci-dessus avant de continuer.</p>
            <?php endif; ?>
            
        <?php elseif ($step == 2): ?>
            <div class="step">Étape 2/4: Configuration de la base de données</div>
            
            <form method="post">
                <input type="hidden" name="step" value="2">
                
                <div class="form-group">
                    <label for="host">Hôte de la base de données:</label>
                    <input type="text" id="host" name="host" value="localhost" required>
                </div>
                
                <div class="form-group">
                    <label for="username">Nom d'utilisateur:</label>
                    <input type="text" id="username" name="username" value="root" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Mot de passe:</label>
                    <input type="password" id="password" name="password" value="">
                </div>
                
                <div class="form-group">
                    <label for="dbname">Nom de la base de données:</label>
                    <input type="text" id="dbname" name="dbname" value="social_scheduler" required>
                </div>
                
                <button type="submit">Tester la connexion →</button>
            </form>
            
        <?php elseif ($step == 3): ?>
            <div class="step">Étape 3/4: Import du schéma de base de données</div>
            
            <p>La connexion à la base de données a été configurée avec succès. Nous allons maintenant importer le schéma de base de données.</p>
            
            <form method="post">
                <input type="hidden" name="step" value="3">
                <button type="submit">Importer le schéma →</button>
            </form>
            
        <?php elseif ($step == 4): ?>
            <div class="step">Étape 4/4: Finalisation</div>
            
            <p>Le schéma de base de données a été importé avec succès. Finalisons l'installation.</p>
            
            <form method="post">
                <input type="hidden" name="step" value="4">
                <button type="submit">Finaliser l'installation →</button>
            </form>
            
        <?php elseif ($step == 5): ?>
            <div class="step">Installation terminée !</div>
            
            <div class="success">
                <h3>🎉 Félicitations !</h3>
                <p>Social Scheduler a été installé avec succès.</p>
            </div>
            
            <h3>Comptes de test disponibles:</h3>
            <ul>
                <li><strong>Admin:</strong> <EMAIL> / password</li>
                <li><strong>Demo:</strong> <EMAIL> / password</li>
            </ul>
            
            <h3>Prochaines étapes:</h3>
            <ol>
                <li>Supprimez les fichiers install.php et test.php pour la sécurité</li>
                <li>Configurez vos clés API dans api/config/database.php</li>
                <li>Personnalisez l'application selon vos besoins</li>
            </ol>
            
            <p style="text-align: center; margin-top: 30px;">
                <a href="index.html" style="background: #4caf50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;">
                    🚀 Lancer l'application
                </a>
            </p>
        <?php endif; ?>
    </div>
</body>
</html>
