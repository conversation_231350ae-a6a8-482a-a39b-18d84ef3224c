<?php
require_once '../config/database.php';
require_once '../config/auth.php';

/**
 * API de création de publications
 */

// Headers CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=UTF-8');

// Gestion des requêtes OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
    exit();
}

try {
    $auth = new Auth();
    $permissions = new Permissions();
    $db = new DatabaseHelper();
    
    // Vérification de l'authentification
    $user = $auth->requireAuth();
    
    // Récupération des données JSON
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Données JSON invalides');
    }
    
    // Validation des champs requis
    if (empty($input['content'])) {
        throw new Exception('Le contenu de la publication est requis');
    }
    
    $title = trim($input['title'] ?? '');
    $content = trim($input['content']);
    $mediaUrls = $input['media_urls'] ?? [];
    $hashtags = $input['hashtags'] ?? [];
    $scheduledAt = $input['scheduled_at'] ?? null;
    $teamId = $input['team_id'] ?? null;
    $socialAccountIds = $input['social_account_ids'] ?? [];
    $status = 'draft';
    
    // Validation du contenu
    if (strlen($content) > 2000) {
        throw new Exception('Le contenu ne peut pas dépasser 2000 caractères');
    }
    
    // Validation de l'équipe si spécifiée
    if ($teamId && !$permissions->canEditTeam($user['id'], $teamId)) {
        throw new Exception('Vous n\'avez pas les permissions pour publier dans cette équipe');
    }
    
    // Vérification des limites du plan
    if (!$permissions->checkPlanLimits($user['id'], 'posts_per_month')) {
        throw new Exception('Limite mensuelle de publications atteinte pour votre plan');
    }
    
    // Validation de la date de planification
    if ($scheduledAt) {
        $scheduledTimestamp = strtotime($scheduledAt);
        if ($scheduledTimestamp === false || $scheduledTimestamp <= time()) {
            throw new Exception('La date de planification doit être dans le futur');
        }
        $status = 'scheduled';
    }
    
    // Validation des comptes sociaux
    if (!empty($socialAccountIds)) {
        $validAccounts = validateSocialAccounts($db, $user['id'], $socialAccountIds);
        if (count($validAccounts) !== count($socialAccountIds)) {
            throw new Exception('Un ou plusieurs comptes sociaux sont invalides');
        }
    }
    
    // Validation des médias
    if (!empty($mediaUrls)) {
        foreach ($mediaUrls as $url) {
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                throw new Exception('URL de média invalide : ' . $url);
            }
        }
    }
    
    // Validation des hashtags
    if (!empty($hashtags)) {
        foreach ($hashtags as $hashtag) {
            if (!preg_match('/^#[a-zA-Z0-9_]+$/', $hashtag)) {
                throw new Exception('Hashtag invalide : ' . $hashtag);
            }
        }
    }
    
    // Début de transaction
    $db->beginTransaction();
    
    try {
        // Insertion de la publication
        $postId = $db->insert('posts', [
            'user_id' => $user['id'],
            'team_id' => $teamId,
            'title' => $title,
            'content' => $content,
            'media_urls' => json_encode($mediaUrls),
            'hashtags' => json_encode($hashtags),
            'status' => $status,
            'scheduled_at' => $scheduledAt
        ]);
        
        // Planification sur les comptes sociaux
        if (!empty($socialAccountIds) && $status === 'scheduled') {
            foreach ($socialAccountIds as $accountId) {
                $db->insert('scheduled_posts', [
                    'post_id' => $postId,
                    'social_account_id' => $accountId,
                    'status' => 'pending',
                    'scheduled_at' => $scheduledAt
                ]);
            }
        }
        
        // Validation de la transaction
        $db->commit();
        
        // Récupération de la publication créée avec les détails
        $post = getPostDetails($db, $postId);
        
        // Log de création
        error_log("Publication créée par {$user['email']} : ID {$postId}");
        
        // Réponse de succès
        http_response_code(201);
        echo json_encode([
            'success' => true,
            'message' => 'Publication créée avec succès',
            'data' => [
                'post' => $post
            ]
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Valide les comptes sociaux de l'utilisateur
 */
function validateSocialAccounts($db, $userId, $accountIds) {
    if (empty($accountIds)) {
        return [];
    }
    
    $placeholders = str_repeat('?,', count($accountIds) - 1) . '?';
    $params = array_merge([$userId], $accountIds);
    
    return $db->fetchAll(
        "SELECT id, platform, account_name FROM social_accounts 
         WHERE user_id = ? AND id IN ({$placeholders}) AND is_active = 1",
        $params
    );
}

/**
 * Récupère les détails complets d'une publication
 */
function getPostDetails($db, $postId) {
    // Publication de base
    $post = $db->fetchOne(
        "SELECT p.*, u.username, u.first_name, u.last_name,
                t.name as team_name
         FROM posts p
         JOIN users u ON p.user_id = u.id
         LEFT JOIN teams t ON p.team_id = t.id
         WHERE p.id = ?",
        [$postId]
    );
    
    if (!$post) {
        return null;
    }
    
    // Décodage des JSON
    $post['media_urls'] = json_decode($post['media_urls'] ?? '[]', true);
    $post['hashtags'] = json_decode($post['hashtags'] ?? '[]', true);
    
    // Comptes sociaux planifiés
    $post['scheduled_accounts'] = $db->fetchAll(
        "SELECT sp.*, sa.platform, sa.account_name
         FROM scheduled_posts sp
         JOIN social_accounts sa ON sp.social_account_id = sa.id
         WHERE sp.post_id = ?",
        [$postId]
    );
    
    return $post;
}
?>
