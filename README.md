# Social Scheduler

Une application complète de planification et gestion des réseaux sociaux, similair<PERSON> <PERSON> Postiz, développée avec HTML, CSS, JavaScript et PHP/MySQL.

## 🚀 Fonctionnalités

### ✅ Implémentées
- **Authentification complète** (inscription, connexion, déconnexion)
- **Tableau de bord** avec statistiques et aperçu
- **Calendrier interactif** pour visualiser les publications
- **Gestion des publications** (création, planification, liste)
- **Comptes de médias sociaux** (connexion, gestion)
- **Analyses et statistiques** détaillées
- **Génération de contenu IA** (simulation)
- **Interface responsive** et moderne
- **Système d'alertes** et notifications

### 🔄 En développement
- Éditeur de publications avancé
- Upload et gestion de médias
- Collaboration d'équipe
- Intégrations API réelles des plateformes
- Application mobile
- Système de templates

## 🛠️ Technologies utilisées

### Frontend
- **HTML5** - Structure sémantique
- **CSS3** - Styles modernes avec variables CSS
- **JavaScript ES6+** - Logique côté client
- **Chart.js** - Graphiques et analyses
- **FullCalendar** - Calendrier interactif
- **Font Awesome** - Icônes

### Backend
- **PHP 7.4+** - API RESTful
- **MySQL 8.0+** - Base de données
- **PDO** - Accès sécurisé à la base de données
- **JWT** - Authentification par tokens

## 📋 Prérequis

- **Serveur web** (Apache/Nginx)
- **PHP 7.4+** avec extensions :
  - PDO
  - PDO_MySQL
  - JSON
  - OpenSSL
- **MySQL 8.0+** ou MariaDB 10.4+
- **Navigateur moderne** supportant ES6+

## 🚀 Installation

### 1. Cloner le projet
```bash
git clone https://github.com/votre-username/social-scheduler.git
cd social-scheduler
```

### 2. Configuration de la base de données
```bash
# Créer la base de données
mysql -u root -p < database/schema.sql
```

### 3. Configuration de l'application
Modifier les paramètres dans `api/config/database.php` :
```php
private $host = 'localhost';
private $db_name = 'social_scheduler';
private $username = 'votre_username';
private $password = 'votre_password';
```

### 4. Configuration du serveur web

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/$1 [L]
```

#### Nginx
```nginx
location /api/ {
    try_files $uri $uri/ /api/index.php?$query_string;
}
```

### 5. Permissions
```bash
chmod 755 api/
chmod 644 api/config/*.php
```

## 📁 Structure du projet

```
social-scheduler/
├── api/                    # API Backend PHP
│   ├── config/            # Configuration
│   │   ├── database.php   # Base de données
│   │   └── auth.php       # Authentification
│   ├── auth/              # Endpoints d'authentification
│   │   ├── login.php
│   │   ├── register.php
│   │   └── logout.php
│   ├── posts/             # Gestion des publications
│   │   ├── create.php
│   │   ├── list.php
│   │   └── schedule.php
│   ├── social/            # Comptes sociaux
│   │   └── connect.php
│   ├── analytics/         # Analyses
│   │   └── stats.php
│   └── ai/                # IA générative
│       └── generate.php
├── css/                   # Styles CSS
│   ├── style.css         # Styles principaux
│   └── dashboard.css     # Styles du dashboard
├── js/                    # JavaScript
│   ├── main.js           # Utilitaires globaux
│   ├── auth.js           # Authentification
│   ├── dashboard.js      # Tableau de bord
│   ├── calendar.js       # Calendrier
│   └── charts.js         # Graphiques
├── database/              # Base de données
│   └── schema.sql        # Schéma et données
├── index.html            # Page d'accueil
├── login.html            # Connexion/Inscription
├── dashboard.html        # Tableau de bord
├── calendar.html         # Calendrier
└── README.md             # Documentation
```

## 🔧 Configuration

### Variables d'environnement
Créer un fichier `.env` (optionnel) :
```env
DB_HOST=localhost
DB_NAME=social_scheduler
DB_USER=root
DB_PASS=password
JWT_SECRET=votre_secret_jwt
OPENAI_API_KEY=votre_cle_openai
```

### Clés API des plateformes
Modifier dans `api/config/database.php` :
```php
const FACEBOOK_APP_ID = 'votre_facebook_app_id';
const TWITTER_API_KEY = 'votre_twitter_api_key';
// ... autres clés
```

## 📖 Utilisation

### 1. Inscription/Connexion
- Accéder à `login.html`
- Créer un compte ou se connecter
- Redirection automatique vers le dashboard

### 2. Tableau de bord
- Vue d'ensemble des statistiques
- Publications récentes
- Comptes connectés
- Actions rapides

### 3. Calendrier
- Visualisation des publications planifiées
- Filtres par plateforme et statut
- Création de nouvelles publications

### 4. Gestion des publications
- Création avec éditeur riche
- Planification multi-plateformes
- Prévisualisation par plateforme
- Génération de contenu IA

## 🔌 API Endpoints

### Authentification
- `POST /api/auth/register.php` - Inscription
- `POST /api/auth/login.php` - Connexion
- `POST /api/auth/logout.php` - Déconnexion

### Publications
- `GET /api/posts/list.php` - Liste des publications
- `POST /api/posts/create.php` - Créer une publication
- `POST /api/posts/schedule.php` - Planifier une publication

### Comptes sociaux
- `POST /api/social/connect.php` - Gérer les comptes

### Analyses
- `GET /api/analytics/stats.php` - Statistiques

### IA
- `POST /api/ai/generate.php` - Génération de contenu

## 🎨 Personnalisation

### Thème et couleurs
Modifier les variables CSS dans `css/style.css` :
```css
:root {
    --primary-color: #3b82f6;
    --secondary-color: #6366f1;
    /* ... autres variables */
}
```

### Ajout de plateformes
1. Ajouter dans l'enum de la base de données
2. Ajouter les icônes et couleurs
3. Implémenter l'API de la plateforme

## 🧪 Tests

### Tests manuels
1. Inscription/Connexion
2. Création de publications
3. Planification
4. Visualisation du calendrier
5. Analyses

### Tests automatisés (à implémenter)
```bash
# Tests PHP
composer test

# Tests JavaScript
npm test
```

## 🚀 Déploiement

### Production
1. Optimiser les assets
2. Configurer HTTPS
3. Sécuriser la base de données
4. Configurer les sauvegardes
5. Monitoring et logs

### Docker (optionnel)
```dockerfile
FROM php:8.0-apache
COPY . /var/www/html/
RUN docker-php-ext-install pdo pdo_mysql
```

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature
3. Commit les changements
4. Push vers la branche
5. Ouvrir une Pull Request

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🆘 Support

- **Documentation** : Voir ce README
- **Issues** : Ouvrir un ticket GitHub
- **Email** : <EMAIL>

## 🔄 Roadmap

### Version 1.1
- [ ] Éditeur de publications avancé
- [ ] Upload de médias
- [ ] Templates de contenu

### Version 1.2
- [ ] Collaboration d'équipe
- [ ] Workflows d'approbation
- [ ] Rôles et permissions

### Version 2.0
- [ ] Application mobile
- [ ] API publique
- [ ] Marketplace de templates

## 📊 Statistiques

- **Lignes de code** : ~5000+
- **Fichiers** : 20+
- **Fonctionnalités** : 15+
- **Plateformes supportées** : 8

---

**Développé avec ❤️ pour la communauté des créateurs de contenu**
