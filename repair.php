<?php
/**
 * Script de réparation automatique pour Social Scheduler
 */

// Affichage des erreurs
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Réparation automatique Social Scheduler</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
    .error { color: red; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
    .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
    .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
    pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
</style>";

try {
    // 1. Vérification et création de la base de données
    echo "<h2>1. Vérification de la base de données</h2>";
    
    require_once 'config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Impossible de se connecter à la base de données");
    }
    
    echo "<div class='success'>✅ Connexion à la base de données réussie</div>";
    
    // 2. Vérification et création des tables
    echo "<h2>2. Vérification des tables</h2>";
    
    $tables_sql = [
        'users' => "
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                email VARCHAR(255) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                first_name VARCHAR(100),
                last_name VARCHAR(100),
                avatar_url VARCHAR(500),
                plan_type ENUM('free', 'pro', 'premium') DEFAULT 'free',
                is_active BOOLEAN DEFAULT TRUE,
                email_verified BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'social_accounts' => "
            CREATE TABLE IF NOT EXISTS social_accounts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                platform ENUM('facebook', 'instagram', 'twitter', 'linkedin', 'tiktok', 'youtube', 'pinterest', 'reddit') NOT NULL,
                account_name VARCHAR(255) NOT NULL,
                account_id VARCHAR(255) NOT NULL,
                access_token TEXT,
                refresh_token TEXT,
                token_expires_at TIMESTAMP NULL,
                profile_picture VARCHAR(500),
                followers_count INT DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_account (user_id, platform, account_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'posts' => "
            CREATE TABLE IF NOT EXISTS posts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                title VARCHAR(255),
                content TEXT NOT NULL,
                media_urls JSON,
                hashtags JSON,
                status ENUM('draft', 'scheduled', 'published', 'failed') DEFAULT 'draft',
                scheduled_at TIMESTAMP NULL,
                published_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'scheduled_posts' => "
            CREATE TABLE IF NOT EXISTS scheduled_posts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                post_id INT NOT NULL,
                social_account_id INT NOT NULL,
                status ENUM('pending', 'published', 'failed', 'draft') DEFAULT 'pending',
                scheduled_at TIMESTAMP NULL,
                published_at TIMESTAMP NULL,
                external_post_id VARCHAR(255),
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
                FOREIGN KEY (social_account_id) REFERENCES social_accounts(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ];
    
    foreach ($tables_sql as $table_name => $sql) {
        try {
            $db->exec($sql);
            echo "<div class='success'>✅ Table $table_name: Créée/Vérifiée</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Erreur table $table_name: " . $e->getMessage() . "</div>";
        }
    }
    
    // 3. Insertion des utilisateurs par défaut
    echo "<h2>3. Création des utilisateurs par défaut</h2>";
    
    $default_users = [
        [
            'email' => '<EMAIL>',
            'password' => password_hash('password', PASSWORD_DEFAULT),
            'first_name' => 'Admin',
            'last_name' => 'System',
            'plan_type' => 'premium',
            'is_active' => 1,
            'email_verified' => 1
        ],
        [
            'email' => '<EMAIL>',
            'password' => password_hash('password', PASSWORD_DEFAULT),
            'first_name' => 'Demo',
            'last_name' => 'User',
            'plan_type' => 'free',
            'is_active' => 1,
            'email_verified' => 1
        ]
    ];
    
    foreach ($default_users as $user) {
        try {
            // Vérifier si l'utilisateur existe déjà
            $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$user['email']]);
            
            if (!$stmt->fetch()) {
                // Créer l'utilisateur
                $stmt = $db->prepare("
                    INSERT INTO users (email, password, first_name, last_name, plan_type, is_active, email_verified) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $user['email'],
                    $user['password'],
                    $user['first_name'],
                    $user['last_name'],
                    $user['plan_type'],
                    $user['is_active'],
                    $user['email_verified']
                ]);
                echo "<div class='success'>✅ Utilisateur {$user['email']}: Créé</div>";
            } else {
                echo "<div class='info'>ℹ️ Utilisateur {$user['email']}: Existe déjà</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Erreur utilisateur {$user['email']}: " . $e->getMessage() . "</div>";
        }
    }
    
    // 4. Vérification des permissions de fichiers
    echo "<h2>4. Vérification des permissions</h2>";
    
    $directories = ['api', 'config', 'js', 'css', 'uploads'];
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "<div class='success'>✅ Dossier $dir: Créé</div>";
            } else {
                echo "<div class='error'>❌ Impossible de créer le dossier $dir</div>";
            }
        } else {
            echo "<div class='info'>ℹ️ Dossier $dir: Existe</div>";
        }
    }
    
    // 5. Test des endpoints critiques
    echo "<h2>5. Test des endpoints</h2>";
    
    $endpoints = [
        'api/auth/login.php' => 'Authentification',
        'api/social/connect.php' => 'Comptes sociaux',
        'api/posts/create_simple.php' => 'Création de posts',
        'api/posts/list.php' => 'Liste des posts'
    ];
    
    foreach ($endpoints as $endpoint => $description) {
        if (file_exists($endpoint)) {
            echo "<div class='success'>✅ $description ($endpoint): Fichier existe</div>";
        } else {
            echo "<div class='error'>❌ $description ($endpoint): Fichier manquant</div>";
        }
    }
    
    echo "<h2>✅ Réparation terminée</h2>";
    echo "<div class='success'>
        <h3>Comptes disponibles:</h3>
        <ul>
            <li><strong>Admin:</strong> <EMAIL> / password</li>
            <li><strong>Demo:</strong> <EMAIL> / password</li>
        </ul>
    </div>";
    
    echo "<div class='info'>
        <h3>Prochaines étapes:</h3>
        <ol>
            <li><a href='login.html'>Se connecter</a></li>
            <li><a href='test_api.html'>Tester l'API</a></li>
            <li><a href='dashboard.html'>Accéder au tableau de bord</a></li>
        </ol>
    </div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur critique: " . $e->getMessage() . "</div>";
    echo "<div class='info'>
        <h3>Solutions possibles:</h3>
        <ul>
            <li>Vérifiez que XAMPP est démarré</li>
            <li>Vérifiez que MySQL fonctionne</li>
            <li>Vérifiez les paramètres dans config/database.php</li>
        </ul>
    </div>";
}
?>
