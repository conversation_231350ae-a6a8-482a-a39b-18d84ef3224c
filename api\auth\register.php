<?php
require_once '../config/database.php';
require_once '../config/auth.php';

/**
 * API d'inscription des utilisateurs
 */

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
    exit();
}

try {
    // Récupération des données JSON
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Données JSON invalides');
    }
    
    // Validation des champs requis
    $requiredFields = ['username', 'email', 'password', 'first_name', 'last_name'];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            throw new Exception("Le champ '{$field}' est requis");
        }
    }
    
    $username = trim($input['username']);
    $email = trim($input['email']);
    $password = $input['password'];
    $firstName = trim($input['first_name']);
    $lastName = trim($input['last_name']);
    $timezone = $input['timezone'] ?? 'UTC';
    $language = $input['language'] ?? 'fr';
    
    // Validation des données
    $auth = new Auth();
    $db = new DatabaseHelper();
    
    // Validation de l'email
    if (!$auth->validateEmail($email)) {
        throw new Exception('Format d\'email invalide');
    }
    
    // Validation du mot de passe
    if (!$auth->validatePassword($password)) {
        throw new Exception('Le mot de passe doit contenir au moins 8 caractères, une majuscule, une minuscule et un chiffre');
    }
    
    // Validation du nom d'utilisateur
    if (strlen($username) < 3 || strlen($username) > 50) {
        throw new Exception('Le nom d\'utilisateur doit contenir entre 3 et 50 caractères');
    }
    
    if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
        throw new Exception('Le nom d\'utilisateur ne peut contenir que des lettres, chiffres et underscores');
    }
    
    // Vérification de l'unicité
    $existingUser = $db->fetchOne(
        "SELECT id FROM users WHERE email = ? OR username = ?",
        [$email, $username]
    );
    
    if ($existingUser) {
        throw new Exception('Un utilisateur avec cet email ou nom d\'utilisateur existe déjà');
    }
    
    // Hachage du mot de passe
    $passwordHash = $auth->hashPassword($password);
    
    // Insertion de l'utilisateur
    $userId = $db->insert('users', [
        'username' => $username,
        'email' => $email,
        'password_hash' => $passwordHash,
        'first_name' => $firstName,
        'last_name' => $lastName,
        'timezone' => $timezone,
        'language' => $language,
        'plan_type' => 'free'
    ]);
    
    // Génération du token JWT
    $token = $auth->generateJWT($userId, $email);
    
    // Génération du token de session
    $sessionToken = $auth->generateSessionToken();
    $expiresAt = date('Y-m-d H:i:s', time() + Config::JWT_EXPIRATION);
    
    // Sauvegarde de la session
    $auth->saveSession($userId, $sessionToken, $expiresAt);
    
    // Récupération des données utilisateur
    $user = $db->fetchOne(
        "SELECT id, username, email, first_name, last_name, avatar_url, plan_type, timezone, language, created_at 
         FROM users WHERE id = ?",
        [$userId]
    );
    
    // Envoi de l'email de bienvenue (simulation)
    $welcomeEmailSent = sendWelcomeEmail($email, $firstName);
    
    // Réponse de succès
    http_response_code(201);
    echo json_encode([
        'success' => true,
        'message' => 'Inscription réussie',
        'data' => [
            'user' => $user,
            'token' => $token,
            'session_token' => $sessionToken,
            'expires_at' => $expiresAt
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Fonction pour envoyer l'email de bienvenue
 */
function sendWelcomeEmail($email, $firstName) {
    // Simulation d'envoi d'email
    // Dans un vrai projet, vous utiliseriez une bibliothèque comme PHPMailer ou SwiftMailer
    
    $subject = "Bienvenue sur " . Config::APP_NAME;
    $message = "
    <html>
    <head>
        <title>Bienvenue sur " . Config::APP_NAME . "</title>
    </head>
    <body>
        <h2>Bonjour {$firstName},</h2>
        <p>Bienvenue sur " . Config::APP_NAME . " !</p>
        <p>Votre compte a été créé avec succès. Vous pouvez maintenant :</p>
        <ul>
            <li>Connecter vos comptes de médias sociaux</li>
            <li>Planifier vos publications</li>
            <li>Analyser vos performances</li>
            <li>Collaborer avec votre équipe</li>
        </ul>
        <p>Commencez dès maintenant en vous connectant à votre tableau de bord.</p>
        <p>
            <a href='" . Config::APP_URL . "/dashboard.html' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>
                Accéder au tableau de bord
            </a>
        </p>
        <p>Merci de nous avoir rejoint !</p>
        <p>L'équipe " . Config::APP_NAME . "</p>
    </body>
    </html>
    ";
    
    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: ' . Config::FROM_NAME . ' <' . Config::FROM_EMAIL . '>',
        'Reply-To: ' . Config::FROM_EMAIL,
        'X-Mailer: PHP/' . phpversion()
    ];
    
    // En production, remplacez par un vrai service d'email
    // return mail($email, $subject, $message, implode("\r\n", $headers));
    
    // Pour le développement, on simule l'envoi
    error_log("Email de bienvenue envoyé à : {$email}");
    return true;
}
?>
