<?php
require_once 'database.php';

/**
 * Classe de gestion de l'authentification
 */
class Auth {
    private $db;
    
    public function __construct() {
        $this->db = new DatabaseHelper();
    }
    
    /**
     * Génère un token JWT
     */
    public function generateJWT($userId, $email) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode([
            'user_id' => $userId,
            'email' => $email,
            'iat' => time(),
            'exp' => time() + Config::JWT_EXPIRATION
        ]);
        
        $headerEncoded = $this->base64UrlEncode($header);
        $payloadEncoded = $this->base64UrlEncode($payload);
        
        $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, Config::JWT_SECRET, true);
        $signatureEncoded = $this->base64UrlEncode($signature);
        
        return $headerEncoded . "." . $payloadEncoded . "." . $signatureEncoded;
    }
    
    /**
     * Vérifie un token JWT
     */
    public function verifyJWT($token) {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return false;
        }
        
        $header = $this->base64UrlDecode($parts[0]);
        $payload = $this->base64UrlDecode($parts[1]);
        $signature = $this->base64UrlDecode($parts[2]);
        
        $expectedSignature = hash_hmac('sha256', $parts[0] . "." . $parts[1], Config::JWT_SECRET, true);
        
        if (!hash_equals($signature, $expectedSignature)) {
            return false;
        }
        
        $payloadData = json_decode($payload, true);
        if ($payloadData['exp'] < time()) {
            return false;
        }
        
        return $payloadData;
    }
    
    /**
     * Récupère l'utilisateur actuel depuis le token
     */
    public function getCurrentUser() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return null;
        }
        
        $token = $matches[1];
        $payload = $this->verifyJWT($token);
        
        if (!$payload) {
            return null;
        }
        
        $user = $this->db->fetchOne(
            "SELECT id, username, email, first_name, last_name, avatar_url, plan_type, timezone, language 
             FROM users WHERE id = ? AND is_active = 1",
            [$payload['user_id']]
        );
        
        return $user;
    }
    
    /**
     * Vérifie si l'utilisateur est authentifié
     */
    public function requireAuth() {
        $user = $this->getCurrentUser();
        if (!$user) {
            http_response_code(401);
            echo json_encode(['error' => 'Token d\'authentification requis']);
            exit();
        }
        return $user;
    }
    
    /**
     * Hache un mot de passe
     */
    public function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Vérifie un mot de passe
     */
    public function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Génère un token de session
     */
    public function generateSessionToken() {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * Sauvegarde une session
     */
    public function saveSession($userId, $token, $expiresAt) {
        return $this->db->insert('user_sessions', [
            'user_id' => $userId,
            'session_token' => $token,
            'expires_at' => $expiresAt
        ]);
    }
    
    /**
     * Supprime une session
     */
    public function deleteSession($token) {
        return $this->db->delete('user_sessions', 'session_token = ?', [$token]);
    }
    
    /**
     * Valide un email
     */
    public function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Valide un mot de passe
     */
    public function validatePassword($password) {
        // Au moins 8 caractères, une majuscule, une minuscule, un chiffre
        return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/', $password);
    }
    
    /**
     * Encode en base64 URL-safe
     */
    private function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    /**
     * Décode depuis base64 URL-safe
     */
    private function base64UrlDecode($data) {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }
}

/**
 * Classe de gestion des permissions
 */
class Permissions {
    private $db;
    
    public function __construct() {
        $this->db = new DatabaseHelper();
    }
    
    /**
     * Vérifie si l'utilisateur peut accéder à une équipe
     */
    public function canAccessTeam($userId, $teamId) {
        $member = $this->db->fetchOne(
            "SELECT role FROM team_members WHERE user_id = ? AND team_id = ?",
            [$userId, $teamId]
        );
        
        return $member !== false;
    }
    
    /**
     * Vérifie si l'utilisateur peut modifier une équipe
     */
    public function canEditTeam($userId, $teamId) {
        $member = $this->db->fetchOne(
            "SELECT role FROM team_members WHERE user_id = ? AND team_id = ? AND role IN ('admin', 'editor')",
            [$userId, $teamId]
        );
        
        return $member !== false;
    }
    
    /**
     * Vérifie si l'utilisateur est propriétaire d'une équipe
     */
    public function isTeamOwner($userId, $teamId) {
        $team = $this->db->fetchOne(
            "SELECT owner_id FROM teams WHERE id = ? AND owner_id = ?",
            [$teamId, $userId]
        );
        
        return $team !== false;
    }
    
    /**
     * Vérifie les limites du plan utilisateur
     */
    public function checkPlanLimits($userId, $limitType) {
        $user = $this->db->fetchOne(
            "SELECT plan_type FROM users WHERE id = ?",
            [$userId]
        );
        
        if (!$user) {
            return false;
        }
        
        $limits = Config::LIMITS[$user['plan_type']];
        
        switch ($limitType) {
            case 'posts_per_month':
                if ($limits['posts_per_month'] === -1) return true;
                
                $postCount = $this->db->fetchOne(
                    "SELECT COUNT(*) as count FROM posts 
                     WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)",
                    [$userId]
                );
                
                return $postCount['count'] < $limits['posts_per_month'];
                
            case 'social_accounts':
                if ($limits['social_accounts'] === -1) return true;
                
                $accountCount = $this->db->fetchOne(
                    "SELECT COUNT(*) as count FROM social_accounts WHERE user_id = ? AND is_active = 1",
                    [$userId]
                );
                
                return $accountCount['count'] < $limits['social_accounts'];
                
            case 'team_members':
                if ($limits['team_members'] === -1) return true;
                
                $memberCount = $this->db->fetchOne(
                    "SELECT COUNT(*) as count FROM team_members tm 
                     JOIN teams t ON tm.team_id = t.id 
                     WHERE t.owner_id = ?",
                    [$userId]
                );
                
                return $memberCount['count'] < $limits['team_members'];
        }
        
        return false;
    }
}
?>
