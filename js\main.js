/**
 * JavaScript principal pour Social Scheduler
 */

// Configuration globale
const CONFIG = {
    API_BASE_URL: 'api',
    APP_NAME: 'Social Scheduler',
    VERSION: '1.0.0',
    DEBUG: true
};

// Utilitaires globaux
const Utils = {
    /**
     * Effectue une requête API
     */
    async apiRequest(endpoint, options = {}) {
        const url = `${CONFIG.API_BASE_URL}/${endpoint}`;
        const token = localStorage.getItem('auth_token');
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...(token && { 'Authorization': `Bearer ${token}` })
            }
        };
        
        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };
        
        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || `HTTP ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    },

    /**
     * Affiche une alerte
     */
    showAlert(message, type = 'info', duration = 5000) {
        const alertContainer = document.getElementById('alert-container') || this.createAlertContainer();
        
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.innerHTML = `
            <div class="alert-content">
                <div class="alert-icon">
                    <i class="fas ${this.getAlertIcon(type)}"></i>
                </div>
                <div class="alert-message">${message}</div>
                <button class="alert-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        alertContainer.appendChild(alert);
        
        // Animation d'entrée
        setTimeout(() => alert.classList.add('show'), 10);
        
        // Suppression automatique
        if (duration > 0) {
            setTimeout(() => {
                alert.classList.remove('show');
                setTimeout(() => alert.remove(), 300);
            }, duration);
        }
    },

    /**
     * Crée le conteneur d'alertes s'il n'existe pas
     */
    createAlertContainer() {
        const container = document.createElement('div');
        container.id = 'alert-container';
        container.className = 'alert-container';
        document.body.appendChild(container);
        return container;
    },

    /**
     * Retourne l'icône appropriée pour le type d'alerte
     */
    getAlertIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    },

    /**
     * Formate une date
     */
    formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        
        return new Intl.DateTimeFormat('fr-FR', { ...defaultOptions, ...options })
            .format(new Date(date));
    },

    /**
     * Formate un nombre
     */
    formatNumber(number, options = {}) {
        return new Intl.NumberFormat('fr-FR', options).format(number);
    },

    /**
     * Débounce une fonction
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * Vérifie si l'utilisateur est authentifié
     */
    isAuthenticated() {
        return !!localStorage.getItem('auth_token');
    },

    /**
     * Redirige vers la page de connexion si non authentifié
     */
    requireAuth() {
        if (!this.isAuthenticated()) {
            window.location.href = 'login.html';
            return false;
        }
        return true;
    },

    /**
     * Déconnecte l'utilisateur
     */
    async logout() {
        try {
            await this.apiRequest('auth/logout.php', {
                method: 'POST'
            });
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            window.location.href = 'login.html';
        }
    },

    /**
     * Copie du texte dans le presse-papiers
     */
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showAlert('Copié dans le presse-papiers', 'success', 2000);
        } catch (error) {
            console.error('Copy error:', error);
            this.showAlert('Erreur lors de la copie', 'error');
        }
    },

    /**
     * Valide un email
     */
    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },

    /**
     * Valide un mot de passe
     */
    validatePassword(password) {
        // Au moins 8 caractères, une majuscule, une minuscule, un chiffre
        const re = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
        return re.test(password);
    },

    /**
     * Génère un ID unique
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    /**
     * Échappe le HTML
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    /**
     * Tronque un texte
     */
    truncateText(text, maxLength = 100) {
        if (text.length <= maxLength) return text;
        return text.substr(0, maxLength) + '...';
    },

    /**
     * Convertit les URLs en liens
     */
    linkify(text) {
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        return text.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener">$1</a>');
    },

    /**
     * Détecte les hashtags
     */
    hashtagify(text) {
        const hashtagRegex = /#(\w+)/g;
        return text.replace(hashtagRegex, '<span class="hashtag">#$1</span>');
    }
};

// Gestionnaire d'événements globaux
const EventManager = {
    events: {},

    /**
     * Ajoute un écouteur d'événement
     */
    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    },

    /**
     * Supprime un écouteur d'événement
     */
    off(event, callback) {
        if (!this.events[event]) return;
        this.events[event] = this.events[event].filter(cb => cb !== callback);
    },

    /**
     * Déclenche un événement
     */
    emit(event, data) {
        if (!this.events[event]) return;
        this.events[event].forEach(callback => callback(data));
    }
};

// Gestionnaire de stockage local
const Storage = {
    /**
     * Sauvegarde des données avec expiration
     */
    set(key, value, expirationMinutes = null) {
        const data = {
            value,
            timestamp: Date.now(),
            expiration: expirationMinutes ? Date.now() + (expirationMinutes * 60 * 1000) : null
        };
        localStorage.setItem(key, JSON.stringify(data));
    },

    /**
     * Récupère des données
     */
    get(key) {
        const item = localStorage.getItem(key);
        if (!item) return null;

        try {
            const data = JSON.parse(item);
            
            // Vérification de l'expiration
            if (data.expiration && Date.now() > data.expiration) {
                localStorage.removeItem(key);
                return null;
            }
            
            return data.value;
        } catch (error) {
            console.error('Storage get error:', error);
            return null;
        }
    },

    /**
     * Supprime des données
     */
    remove(key) {
        localStorage.removeItem(key);
    },

    /**
     * Vide le stockage
     */
    clear() {
        localStorage.clear();
    }
};

// Fonctions globales pour l'interface
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        sidebar.classList.toggle('show');
    }
}

function toggleUserMenu() {
    const dropdown = document.getElementById('user-menu-dropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

function toggleNotifications() {
    const dropdown = document.getElementById('notification-dropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.parentElement.querySelector('.password-toggle');
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

async function logout() {
    if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
        await Utils.logout();
    }
}

// Fermeture des dropdowns en cliquant à l'extérieur
document.addEventListener('click', (e) => {
    const dropdowns = document.querySelectorAll('.user-menu-dropdown, .notification-dropdown');
    dropdowns.forEach(dropdown => {
        if (!dropdown.contains(e.target) && !dropdown.previousElementSibling.contains(e.target)) {
            dropdown.classList.remove('show');
        }
    });
});

// Gestion du menu mobile
document.addEventListener('DOMContentLoaded', () => {
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('show');
        });
    }
});

// Styles CSS pour les alertes
if (!document.getElementById('alert-styles')) {
    const style = document.createElement('style');
    style.id = 'alert-styles';
    style.textContent = `
        .alert-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .alert {
            min-width: 300px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .alert.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        .alert-content {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
        }
        
        .alert-icon {
            flex-shrink: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .alert-message {
            flex: 1;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .alert-close {
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            opacity: 0.7;
        }
        
        .alert-close:hover {
            opacity: 1;
            background: rgba(0, 0, 0, 0.1);
        }
        
        .alert-success {
            border-left: 4px solid #10b981;
        }
        
        .alert-success .alert-icon {
            color: #10b981;
        }
        
        .alert-error {
            border-left: 4px solid #ef4444;
        }
        
        .alert-error .alert-icon {
            color: #ef4444;
        }
        
        .alert-warning {
            border-left: 4px solid #f59e0b;
        }
        
        .alert-warning .alert-icon {
            color: #f59e0b;
        }
        
        .alert-info {
            border-left: 4px solid #06b6d4;
        }
        
        .alert-info .alert-icon {
            color: #06b6d4;
        }
    `;
    document.head.appendChild(style);
}

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Utils, EventManager, Storage };
}
