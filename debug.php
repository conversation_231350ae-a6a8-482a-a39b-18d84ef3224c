<?php
/**
 * Script de diagnostic pour Social Scheduler
 */

// Affichage des erreurs
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Diagnostic Social Scheduler</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
    .error { color: red; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
    .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
    .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
    pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

// 1. Vérification de la configuration PHP
echo "<h2>1. Configuration PHP</h2>";
echo "<div class='info'>Version PHP: " . phpversion() . "</div>";

$required_extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<div class='success'>✅ Extension $ext: Activée</div>";
    } else {
        echo "<div class='error'>❌ Extension $ext: Manquante</div>";
    }
}

// 2. Vérification de la base de données
echo "<h2>2. Connexion à la base de données</h2>";

try {
    require_once 'config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<div class='success'>✅ Connexion à la base de données: Réussie</div>";
        
        // Vérifier les tables
        echo "<h3>Tables existantes:</h3>";
        $stmt = $db->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($tables)) {
            echo "<div class='warning'>⚠️ Aucune table trouvée. L'installation n'est pas complète.</div>";
            echo "<div class='info'>👉 <a href='install.php'>Cliquez ici pour installer</a></div>";
        } else {
            echo "<table>";
            echo "<tr><th>Table</th><th>Nombre d'enregistrements</th></tr>";
            
            foreach ($tables as $table) {
                try {
                    $stmt = $db->query("SELECT COUNT(*) FROM `$table`");
                    $count = $stmt->fetchColumn();
                    echo "<tr><td>$table</td><td>$count</td></tr>";
                } catch (Exception $e) {
                    echo "<tr><td>$table</td><td style='color: red;'>Erreur: " . $e->getMessage() . "</td></tr>";
                }
            }
            echo "</table>";
        }
        
    } else {
        echo "<div class='error'>❌ Impossible de se connecter à la base de données</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur de connexion: " . $e->getMessage() . "</div>";
    echo "<div class='info'>Vérifiez les paramètres dans config/database.php</div>";
}

// 3. Vérification des fichiers de configuration
echo "<h2>3. Fichiers de configuration</h2>";

$config_files = [
    'config/database.php',
    'config/auth.php',
    'api/auth/login.php',
    'api/social/connect.php',
    'api/posts/create_simple.php'
];

foreach ($config_files as $file) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ $file: Existe</div>";
    } else {
        echo "<div class='error'>❌ $file: Manquant</div>";
    }
}

// 4. Test des endpoints API
echo "<h2>4. Test des endpoints API</h2>";

// Test de connexion
echo "<h3>Test de connexion:</h3>";
try {
    $login_data = json_encode([
        'email' => '<EMAIL>',
        'password' => 'password'
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $login_data
        ]
    ]);
    
    $result = file_get_contents('http://localhost/Post/api/auth/login.php', false, $context);
    $response = json_decode($result, true);
    
    if ($response && isset($response['success'])) {
        if ($response['success']) {
            echo "<div class='success'>✅ API de connexion: Fonctionne</div>";
        } else {
            echo "<div class='warning'>⚠️ API de connexion: " . ($response['message'] ?? 'Erreur inconnue') . "</div>";
        }
    } else {
        echo "<div class='error'>❌ API de connexion: Réponse invalide</div>";
        echo "<pre>" . htmlspecialchars($result) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur test API: " . $e->getMessage() . "</div>";
}

// 5. Vérification des permissions
echo "<h2>5. Permissions des fichiers</h2>";

$directories = ['api', 'config', 'js', 'css'];
foreach ($directories as $dir) {
    if (is_readable($dir)) {
        echo "<div class='success'>✅ Dossier $dir: Accessible en lecture</div>";
    } else {
        echo "<div class='error'>❌ Dossier $dir: Non accessible</div>";
    }
}

// 6. Variables d'environnement
echo "<h2>6. Variables d'environnement</h2>";
echo "<div class='info'>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</div>";
echo "<div class='info'>Script Name: " . $_SERVER['SCRIPT_NAME'] . "</div>";
echo "<div class='info'>Server Name: " . $_SERVER['SERVER_NAME'] . "</div>";

// 7. Logs d'erreurs récents
echo "<h2>7. Dernières erreurs PHP</h2>";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    $errors = file_get_contents($error_log);
    $recent_errors = array_slice(explode("\n", $errors), -10);
    echo "<pre>" . htmlspecialchars(implode("\n", $recent_errors)) . "</pre>";
} else {
    echo "<div class='info'>Aucun log d'erreur trouvé ou configuré</div>";
}

echo "<h2>🔧 Actions recommandées</h2>";
echo "<div class='info'>
<ol>
<li>Si les tables sont manquantes: <a href='install.php'>Exécuter l'installation</a></li>
<li>Si erreur de connexion DB: Vérifier XAMPP et MySQL</li>
<li>Si erreur API: Vérifier les logs ci-dessus</li>
<li>Tester avec: <a href='test_api.html'>Page de test API</a></li>
</ol>
</div>";

?>
