<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Publications - Social Scheduler</title>
    <meta name="description" content="Gérez toutes vos publications sur les réseaux sociaux depuis Social Scheduler.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="index.html" class="logo">
                <i class="fas fa-calendar-alt"></i>
                <span>Social Scheduler</span>
            </a>
            <button class="sidebar-toggle" id="sidebar-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="dashboard.html" class="nav-link">
                        <i class="fas fa-home"></i>
                        <span>Tableau de bord</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="calendar.html" class="nav-link">
                        <i class="fas fa-calendar"></i>
                        <span>Calendrier</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="posts.html" class="nav-link active">
                        <i class="fas fa-file-alt"></i>
                        <span>Publications</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showCreatePostModal()">
                        <i class="fas fa-plus"></i>
                        <span>Nouvelle publication</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="analytics.html" class="nav-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analyses</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="social-accounts.html" class="nav-link">
                        <i class="fas fa-share-alt"></i>
                        <span>Comptes sociaux</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>Équipe</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-images"></i>
                        <span>Bibliothèque</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="settings.html" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Paramètres</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="sidebar-footer">
            <div class="user-info" id="user-info">
                <div class="user-avatar">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="Avatar" id="user-avatar">
                </div>
                <div class="user-details">
                    <div class="user-name" id="user-name">Chargement...</div>
                    <div class="user-plan" id="user-plan">Plan Free</div>
                </div>
                <div class="user-menu">
                    <button class="user-menu-btn" onclick="toggleUserMenu()">
                        <i class="fas fa-chevron-up"></i>
                    </button>
                    <div class="user-menu-dropdown" id="user-menu-dropdown">
                        <a href="settings.html" class="menu-item">
                            <i class="fas fa-user"></i>
                            Profil
                        </a>
                        <a href="#" class="menu-item">
                            <i class="fas fa-crown"></i>
                            Upgrade
                        </a>
                        <a href="#" class="menu-item" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            Déconnexion
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-btn" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title">Publications</h1>
            </div>
            <div class="header-right">
                <div class="posts-filters">
                    <select id="status-filter" class="filter-select">
                        <option value="">Tous les statuts</option>
                        <option value="draft">Brouillons</option>
                        <option value="scheduled">Planifiées</option>
                        <option value="published">Publiées</option>
                        <option value="failed">Échecs</option>
                    </select>
                    <select id="platform-filter" class="filter-select">
                        <option value="">Toutes les plateformes</option>
                        <option value="facebook">Facebook</option>
                        <option value="instagram">Instagram</option>
                        <option value="twitter">Twitter</option>
                        <option value="linkedin">LinkedIn</option>
                        <option value="tiktok">TikTok</option>
                        <option value="youtube">YouTube</option>
                    </select>
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="search-input" placeholder="Rechercher dans les publications...">
                    </div>
                </div>
                <button class="btn btn-primary" onclick="showCreatePostModal()">
                    <i class="fas fa-plus"></i>
                    Nouvelle publication
                </button>
            </div>
        </header>

        <!-- Posts Content -->
        <div class="posts-content">
            <!-- Stats Bar -->
            <div class="posts-stats">
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="total-posts">0</div>
                        <div class="stat-label">Total</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="scheduled-posts">0</div>
                        <div class="stat-label">Planifiées</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="published-posts">0</div>
                        <div class="stat-label">Publiées</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="draft-posts">0</div>
                        <div class="stat-label">Brouillons</div>
                    </div>
                </div>
            </div>

            <!-- Posts List -->
            <div class="posts-list-container">
                <div class="posts-list-header">
                    <div class="list-controls">
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="grid" title="Vue grille">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="view-btn" data-view="list" title="Vue liste">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                        <div class="sort-controls">
                            <select id="sort-by" class="sort-select">
                                <option value="created_at">Date de création</option>
                                <option value="scheduled_at">Date de planification</option>
                                <option value="title">Titre</option>
                                <option value="status">Statut</option>
                            </select>
                            <button class="sort-order-btn" id="sort-order" data-order="desc" title="Ordre décroissant">
                                <i class="fas fa-sort-amount-down"></i>
                            </button>
                        </div>
                        <div class="bulk-actions" id="bulk-actions" style="display: none;">
                            <span class="selected-count" id="selected-count">0 sélectionnée(s)</span>
                            <button class="btn btn-sm btn-secondary" onclick="bulkEdit()">
                                <i class="fas fa-edit"></i>
                                Modifier
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="bulkSchedule()">
                                <i class="fas fa-clock"></i>
                                Planifier
                            </button>
                            <button class="btn btn-sm btn-error" onclick="bulkDelete()">
                                <i class="fas fa-trash"></i>
                                Supprimer
                            </button>
                        </div>
                    </div>
                </div>

                <div class="posts-list" id="posts-list">
                    <!-- Posts will be loaded here -->
                    <div class="loading-container">
                        <div class="loading-spinner">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                        <p>Chargement des publications...</p>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination-container" id="pagination-container" style="display: none;">
                    <div class="pagination-info">
                        <span id="pagination-info">Affichage de 1-10 sur 25 publications</span>
                    </div>
                    <div class="pagination" id="pagination">
                        <!-- Pagination buttons will be generated here -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Create/Edit Post Modal -->
    <div id="post-modal" class="modal" style="display: none;">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title">Nouvelle publication</h3>
                <button class="modal-close" onclick="closePostModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="post-form" class="post-form">
                    <input type="hidden" id="post-id" name="post_id">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="post-title" class="form-label">Titre (optionnel)</label>
                            <input type="text" id="post-title" name="title" class="form-input" placeholder="Titre de votre publication">
                        </div>
                        <div class="form-group">
                            <label for="post-status" class="form-label">Statut</label>
                            <select id="post-status" name="status" class="form-select">
                                <option value="draft">Brouillon</option>
                                <option value="scheduled">Planifier</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="post-content" class="form-label">Contenu</label>
                        <textarea id="post-content" name="content" class="form-textarea" rows="6" placeholder="Rédigez votre publication..." required></textarea>
                        <div class="content-tools">
                            <button type="button" class="btn btn-sm btn-secondary" onclick="showAIAssistant()">
                                <i class="fas fa-robot"></i>
                                Assistant IA
                            </button>
                            <span class="character-count" id="character-count">0/280</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Plateformes</label>
                        <div class="platforms-selector" id="platforms-selector">
                            <!-- Platforms will be loaded here -->
                        </div>
                    </div>
                    
                    <div class="form-group" id="schedule-group" style="display: none;">
                        <label for="scheduled-at" class="form-label">Date et heure de publication</label>
                        <input type="datetime-local" id="scheduled-at" name="scheduled_at" class="form-input">
                    </div>
                    
                    <div class="form-group">
                        <label for="post-hashtags" class="form-label">Hashtags</label>
                        <input type="text" id="post-hashtags" name="hashtags" class="form-input" placeholder="#marketing #socialmedia">
                        <div class="form-help">
                            <small>Séparez les hashtags par des espaces</small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closePostModal()">Annuler</button>
                <button class="btn btn-primary" onclick="savePost()">
                    <span class="btn-text">Sauvegarder</span>
                    <div class="btn-loader" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </button>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div id="alert-container"></div>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/posts.js"></script>
</body>
</html>
