{"name": "gitroom", "version": "1.0.0", "description": "", "main": "index.js", "license": "AGPL-3.0", "keywords": [], "author": "", "engines": {"node": ">=20.0.0 <21.0.0"}, "packageManager": "pnpm@10.6.1", "scripts": {"dev": "pnpm run --filter ./apps/extension --filter ./apps/workers --filter ./apps/backend --filter ./apps/frontend --parallel dev", "pm2": "pnpm dlx concurrently \"pnpm run pm2-run\" \"pnpm run entryfile\"", "entryfile": "./entrypoint.sh", "pm2-run": "pm2 delete all || true && pnpm run prisma-db-push && pnpm run --parallel pm2 && pm2 logs", "dev:stripe": "pnpm dlx concurrently \"stripe listen --forward-to localhost:3000/stripe\" \"pnpm run dev\"", "build": "pnpm -r --workspace-concurrency=1 --filter ./apps/frontend --filter ./apps/backend --filter ./apps/workers  --filter ./apps/cron run build", "build:backend": "rm -rf apps/backend/dist && pnpm --filter ./apps/backend run build", "build:frontend": "rm -rf apps/frontend/dist && pnpm --filter ./apps/frontend run build", "build:workers": "rm -rf apps/workers/dist && pnpm --filter ./apps/workers run build", "build:cron": "rm -rf apps/cron/dist && pnpm --filter ./apps/cron run build", "build:extension": "rm -rf apps/extension/dist && pnpm --filter ./apps/extension run build", "dev:backend": "rm -rf apps/backend/dist && pnpm --filter ./apps/backend run dev", "dev:frontend": "rm -rf apps/frontend/dist && pnpm --filter ./apps/frontend run dev", "dev:workers": "rm -rf apps/workers/dist && pnpm --filter ./apps/workers run dev", "dev:cron": "rm -rf apps/cron/dist && pnpm --filter ./apps/cron run dev", "start:prod:backend": "pnpm --filter ./apps/backend run start", "start:prod:frontend": "pnpm --filter ./apps/frontend run start", "start:prod:workers": "pnpm --filter ./apps/workers run start", "start:prod:cron": "pnpm --filter ./apps/cron run start", "update-plugins": "node build.plugins.js", "dev:docker": "docker compose -f ./docker-compose.dev.yaml up -d", "commands:build:development": "pnpm --filter ./apps/commands run build", "workers": "rm -rf dist/workers && pnpm --filter ./apps/workers run dev", "cron": "rm -rf dist/cron && pnpm --filter ./apps/cron run dev", "prisma-generate": "pnpm dlx prisma generate --schema ./libraries/nestjs-libraries/src/database/prisma/schema.prisma", "prisma-db-push": "pnpm dlx prisma db push --schema ./libraries/nestjs-libraries/src/database/prisma/schema.prisma", "prisma-reset": "cd ./libraries/nestjs-libraries/src/database/prisma && pnpm dlx prisma db push --force-reset && npx prisma db push", "docker-build": "./var/docker/docker-build.sh", "docker-create": "./var/docker/docker-create.sh", "postinstall": "pnpm run update-plugins && pnpm run prisma-generate", "test": "jest --coverage --detect<PERSON><PERSON>Handles --reporters=default --reporters=jest-junit"}, "dependencies": {"@atproto/api": "^0.14.21", "@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@casl/ability": "^6.5.0", "@copilotkit/react-core": "^1.8.9", "@copilotkit/react-textarea": "^1.8.9", "@copilotkit/react-ui": "^1.8.9", "@copilotkit/runtime": "^1.8.9", "@hookform/resolvers": "^3.3.4", "@langchain/community": "^0.3.40", "@langchain/core": "^0.3.44", "@langchain/langgraph": "^0.2.63", "@langchain/openai": "^0.5.5", "@mantine/core": "^5.10.5", "@mantine/dates": "^5.10.5", "@mantine/hooks": "^5.10.5", "@mantine/modals": "^5.10.5", "@modelcontextprotocol/sdk": "^1.9.0", "@nestjs/cli": "10.0.2", "@nestjs/common": "^10.0.2", "@nestjs/core": "^10.0.2", "@nestjs/microservices": "^10.3.1", "@nestjs/platform-express": "^10.0.2", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.3.0", "@nestjs/throttler": "^6.3.0", "@neynar/nodejs-sdk": "^2.8.1", "@neynar/react": "^0.9.7", "@nx/eslint": "19.7.2", "@nx/eslint-plugin": "19.7.2", "@nx/jest": "19.7.2", "@nx/js": "19.7.2", "@nx/nest": "19.7.2", "@nx/next": "19.7.2", "@nx/node": "19.7.2", "@nx/react": "19.7.2", "@nx/vite": "19.7.2", "@nx/webpack": "19.7.2", "@nx/workspace": "19.7.2", "@postiz/wallets": "^0.0.1", "@prisma/client": "^6.5.0", "@solana/wallet-adapter-react": "^0.15.35", "@solana/wallet-adapter-react-ui": "^0.9.35", "@swc/helpers": "0.5.13", "@sweetalert2/theme-dark": "^5.0.16", "@tailwindcss/postcss": "^4.1.7", "@types/bcrypt": "^5.0.2", "@types/concat-stream": "^2.0.3", "@types/facebook-nodejs-business-sdk": "^20.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/md5": "^2.3.5", "@types/mime": "^3.0.4", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.16", "@types/react-dropzone": "^4.2.2", "@types/remove-markdown": "^0.3.4", "@types/sha256": "^0.2.2", "@types/stripe": "^8.0.417", "@types/striptags": "^0.0.5", "@types/yup": "^0.32.0", "@uidotdev/usehooks": "^2.4.1", "@uiw/react-md-editor": "^4.0.3", "@uppy/aws-s3": "^4.1.0", "@uppy/compressor": "^2.1.1", "@uppy/core": "^4.2.0", "@uppy/dashboard": "^4.1.0", "@uppy/drag-drop": "^4.0.2", "@uppy/file-input": "^4.0.1", "@uppy/progress-bar": "^4.0.0", "@uppy/react": "^4.0.2", "@uppy/status-bar": "^4.0.3", "@uppy/xhr-upload": "^4.1.0", "accept-language": "^3.0.20", "ai": "^4.0.22", "algoliasearch": "^5.18.0", "array-move": "^4.0.0", "axios": "^1.7.7", "bcrypt": "^5.1.1", "bs58": "^6.0.0", "bufferutil": "^4.0.8", "bullmq": "^5.12.12", "canvas": "^2.11.2", "chart.js": "^4.4.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "clsx": "^2.1.0", "concat-stream": "^2.0.0", "cookie-parser": "^1.4.7", "copy-to-clipboard": "^3.3.3", "crypto-hash": "^3.0.0", "dayjs": "^1.11.10", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "emoji-picker-react": "^4.12.0", "facebook-nodejs-business-sdk": "^21.0.5", "fast-xml-parser": "^4.5.1", "google-auth-library": "^9.11.0", "googleapis": "^137.1.0", "hot-reload-extension-vite": "^1.0.13", "i18n-iso-countries": "^7.14.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-resources-to-backend": "^1.2.1", "image-to-pdf": "^3.0.2", "ioredis": "^5.3.2", "json-to-graphql-query": "^2.2.5", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "md5": "^2.3.0", "mime": "^3.0.0", "mime-types": "^2.1.35", "multer": "^1.4.5-lts.1", "nestjs-command": "^3.1.4", "nestjs-real-ip": "^3.0.1", "next": "^14.2.14", "next-i18next": "^15.4.2", "next-plausible": "^3.12.0", "node-telegram-bot-api": "^0.66.0", "nodemailer": "^6.9.15", "nostr-tools": "^2.10.4", "nx": "19.7.2", "openai": "^4.47.1", "polotno": "^2.10.5", "posthog-js": "^1.178.0", "react": "18.3.1", "react-colorful": "^5.6.1", "react-country-flag": "^3.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "18.3.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.50.1", "react-i18next": "^15.5.2", "react-loading": "^2.0.3", "react-tag-autocomplete": "^7.2.0", "react-tooltip": "^5.26.2", "react-use-cookie": "^1.6.1", "react-use-keypress": "^1.3.1", "redis": "^4.6.12", "reflect-metadata": "^0.1.13", "remove-markdown": "^0.5.0", "resend": "^3.2.0", "rss-parser": "^3.13.0", "rxjs": "^7.8.0", "sha256": "^0.2.0", "sharp": "^0.33.4", "simple-statistics": "^7.8.3", "stripe": "^15.5.0", "striptags": "^3.2.0", "sweetalert2": "11.4.8", "swr": "^2.2.5", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "3.4.17", "tailwindcss-rtl": "^0.9.0", "tldts": "^6.1.47", "tslib": "^2.3.0", "tweetnacl": "^1.0.3", "twitter-api-v2": "^1.23.2", "twitter-text": "^3.1.0", "use-debounce": "^10.0.0", "utf-8-validate": "^5.0.10", "uuid": "^10.0.0", "viem": "^2.22.9", "webextension-polyfill": "^0.12.0", "ws": "^8.18.0", "yargs": "^17.7.2", "yup": "^1.4.0", "zod": "^3.24.1"}, "devDependencies": {"@crxjs/vite-plugin": "^2.0.0-beta.32", "@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.0.2", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@svgr/webpack": "^8.0.1", "@swc-node/register": "1.9.2", "@swc/cli": "0.3.14", "@swc/core": "1.5.7", "@tailwindcss/vite": "^4.0.17", "@testing-library/react": "15.0.6", "@types/cache-manager-redis-store": "^2.0.4", "@types/chrome": "^0.0.319", "@types/cookie-parser": "^1.4.6", "@types/jest": "29.5.12", "@types/node": "18.16.9", "@types/node-telegram-bot-api": "^0.64.7", "@types/react": "18.3.1", "@types/react-dom": "18.3.0", "@types/uuid": "^9.0.8", "@types/webextension-polyfill": "^0.12.3", "@types/yargs": "^17.0.32", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "@vitejs/plugin-react": "^4.2.0", "@vitest/coverage-v8": "1.6.0", "@vitest/ui": "1.6.0", "autoprefixer": "^10.4.17", "babel-jest": "29.7.0", "eslint": "8.57.0", "eslint-config-next": "15.2.1", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "fs-extra": "^11.3.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-environment-node": "^29.4.1", "jest-junit": "^16.0.0", "jest-mock-extended": "^4.0.0-beta1", "jsdom": "~22.1.0", "nodemon": "^3.1.9", "postcss": "8.4.38", "prettier": "^2.6.2", "prisma": "^6.5.0", "react-refresh": "^0.10.0", "sass": "1.62.1", "ts-jest": "^29.1.0", "ts-node": "10.9.2", "typescript": "5.5.4", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4", "vitest": "3.1.4"}, "volta": {"node": "20.17.0"}, "jest-junit": {"outputDirectory": "./reports", "outputName": "junit.xml"}, "pnpm": {"onlyBuiltDependencies": ["bcrypt"]}}